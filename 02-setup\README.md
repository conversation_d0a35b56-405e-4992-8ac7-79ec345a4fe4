# 第2章：环境搭建

## 🎯 学习目标
- 安装Node.js和npm
- 安装TypeScript编译器
- 配置开发环境
- 创建第一个TypeScript项目

## 📋 前置要求

在开始之前，您需要：
- 基本的命令行操作知识
- 文本编辑器或IDE（推荐VS Code）

## 🛠️ 安装步骤

### 1. 安装Node.js和npm

TypeScript需要Node.js环境来运行。

#### Windows系统：
1. 访问 [Node.js官网](https://nodejs.org/)
2. 下载LTS版本（推荐）
3. 运行安装程序，按默认设置安装

#### macOS系统：
```bash
# 使用Homebrew安装
brew install node

# 或者从官网下载安装包
```

#### Linux系统：
```bash
# Ubuntu/Debian
sudo apt update
sudo apt install nodejs npm

# CentOS/RHEL
sudo yum install nodejs npm
```

#### 验证安装：
```bash
node --version
npm --version
```

### 2. 安装TypeScript

#### 全局安装（推荐用于学习）：
```bash
npm install -g typescript
```

#### 验证TypeScript安装：
```bash
tsc --version
```

#### 项目本地安装：
```bash
# 在项目目录中
npm install --save-dev typescript
```

### 3. 安装开发工具

#### VS Code（强烈推荐）
1. 下载：[Visual Studio Code](https://code.visualstudio.com/)
2. 安装有用的扩展：
   - TypeScript Importer
   - Prettier - Code formatter
   - ESLint
   - Auto Rename Tag

#### 其他编辑器选择：
- WebStorm
- Sublime Text
- Atom
- Vim/Neovim

## 🚀 创建第一个TypeScript项目

### 1. 创建项目目录
```bash
mkdir my-first-ts-project
cd my-first-ts-project
```

### 2. 初始化npm项目
```bash
npm init -y
```

### 3. 创建TypeScript配置文件
```bash
tsc --init
```

这会创建一个`tsconfig.json`文件，包含TypeScript编译器的配置。

### 4. 创建源代码目录结构
```
my-first-ts-project/
├── src/
│   └── index.ts
├── dist/
├── package.json
└── tsconfig.json
```

### 5. 编写第一个TypeScript文件

创建 `src/index.ts`：
```typescript
// src/index.ts
function greet(name: string): string {
    return `Hello, ${name}! Welcome to TypeScript!`;
}

const userName: string = "TypeScript学习者";
console.log(greet(userName));

// 展示类型检查的威力
// console.log(greet(123)); // 这行会报错！
```

### 6. 编译TypeScript代码
```bash
# 编译单个文件
tsc src/index.ts

# 编译整个项目（使用tsconfig.json配置）
tsc

# 监听模式（自动重新编译）
tsc --watch
```

### 7. 运行编译后的JavaScript
```bash
node dist/index.js
```

## ⚙️ 配置tsconfig.json

基本的`tsconfig.json`配置：

```json
{
  "compilerOptions": {
    "target": "ES2020",                    // 编译目标
    "module": "commonjs",                  // 模块系统
    "outDir": "./dist",                    // 输出目录
    "rootDir": "./src",                    // 源代码目录
    "strict": true,                        // 启用严格模式
    "esModuleInterop": true,              // ES模块互操作
    "skipLibCheck": true,                 // 跳过库文件检查
    "forceConsistentCasingInFileNames": true // 强制文件名大小写一致
  },
  "include": [
    "src/**/*"                            // 包含的文件
  ],
  "exclude": [
    "node_modules",                       // 排除的目录
    "dist"
  ]
}
```

## 📦 设置开发脚本

在`package.json`中添加有用的脚本：

```json
{
  "scripts": {
    "build": "tsc",
    "start": "node dist/index.js",
    "dev": "tsc --watch",
    "clean": "rm -rf dist"
  }
}
```

现在可以使用：
```bash
npm run build    # 编译
npm run start    # 运行
npm run dev      # 开发模式（监听文件变化）
npm run clean    # 清理输出目录
```

## 🔧 安装类型定义

对于使用的JavaScript库，可能需要安装类型定义：

```bash
# 安装Node.js类型定义
npm install --save-dev @types/node

# 其他常用类型定义
npm install --save-dev @types/lodash
npm install --save-dev @types/express
```

## 🎯 验证环境搭建

创建一个测试文件来验证环境：

```typescript
// test-setup.ts
interface User {
    name: string;
    age: number;
}

const user: User = {
    name: "张三",
    age: 25
};

function displayUser(user: User): void {
    console.log(`用户：${user.name}，年龄：${user.age}`);
}

displayUser(user);
```

编译并运行：
```bash
tsc test-setup.ts
node test-setup.js
```

如果看到输出"用户：张三，年龄：25"，说明环境搭建成功！

## 🚨 常见问题解决

### 1. 命令找不到
- 确保Node.js和npm正确安装
- 检查环境变量PATH设置
- 重启终端或命令行

### 2. 权限问题（macOS/Linux）
```bash
# 使用sudo安装全局包
sudo npm install -g typescript
```

### 3. 网络问题
```bash
# 使用淘宝镜像
npm config set registry https://registry.npmmirror.com
```

## 🎯 本章小结

现在您已经成功搭建了TypeScript开发环境！您学会了：
- 安装Node.js、npm和TypeScript
- 创建TypeScript项目
- 配置编译选项
- 编译和运行TypeScript代码

## 🔗 有用的资源

- [Node.js官网](https://nodejs.org/)
- [TypeScript配置参考](https://www.typescriptlang.org/tsconfig)
- [VS Code TypeScript支持](https://code.visualstudio.com/docs/languages/typescript)

---

**下一章：[基本类型](../03-basic-types/README.md)**
