# 第3章：基本类型

## 🎯 学习目标
- 掌握TypeScript的基本数据类型
- 理解类型注解的语法
- 学会使用联合类型和字面量类型
- 了解类型推断机制

## 📖 TypeScript类型系统概述

TypeScript的类型系统是其核心特性，它提供了静态类型检查，帮助我们在编译时发现错误。

### 类型注解语法
```typescript
let variableName: type = value;
```

## 🔢 基本数据类型

### 1. 数字类型 (number)
```typescript
let age: number = 25;
let price: number = 99.99;
let hexNumber: number = 0xf00d;    // 十六进制
let binaryNumber: number = 0b1010; // 二进制
let octalNumber: number = 0o744;   // 八进制

// 特殊数值
let notANumber: number = NaN;
let infinityValue: number = Infinity;
```

### 2. 字符串类型 (string)
```typescript
let firstName: string = "张";
let lastName: string = '三';
let fullName: string = `${firstName}${lastName}`; // 模板字符串

// 多行字符串
let poem: string = `
    床前明月光，
    疑是地上霜。
    举头望明月，
    低头思故乡。
`;
```

### 3. 布尔类型 (boolean)
```typescript
let isStudent: boolean = true;
let isGraduated: boolean = false;
let isActive: boolean = Boolean(1); // true
```

### 4. 数组类型 (Array)
```typescript
// 方式1：类型[]
let numbers: number[] = [1, 2, 3, 4, 5];
let names: string[] = ["Alice", "Bob", "Charlie"];

// 方式2：Array<类型>
let scores: Array<number> = [85, 92, 78, 96];
let cities: Array<string> = ["北京", "上海", "广州"];

// 混合类型数组（使用联合类型）
let mixedArray: (string | number)[] = ["hello", 42, "world", 100];
```

### 5. 元组类型 (Tuple)
元组表示已知元素数量和类型的数组，各元素的类型不必相同。

```typescript
// 定义元组
let person: [string, number] = ["张三", 25];
let coordinate: [number, number] = [10, 20];

// 访问元组元素
console.log(person[0]); // "张三"
console.log(person[1]); // 25

// 元组解构
let [name, age] = person;

// 可选元素
let optionalTuple: [string, number?] = ["Alice"];

// 剩余元素
let restTuple: [string, ...number[]] = ["grades", 85, 92, 78];
```

### 6. 枚举类型 (enum)
```typescript
// 数字枚举
enum Direction {
    Up,    // 0
    Down,  // 1
    Left,  // 2
    Right  // 3
}

let dir: Direction = Direction.Up;

// 指定起始值
enum Status {
    Pending = 1,
    Approved,  // 2
    Rejected   // 3
}

// 字符串枚举
enum Color {
    Red = "red",
    Green = "green",
    Blue = "blue"
}

// 常量枚举（编译时内联）
const enum Size {
    Small = "S",
    Medium = "M",
    Large = "L"
}
```

## 🔄 特殊类型

### 1. any 类型
```typescript
let anything: any = 42;
anything = "hello";
anything = true;
anything = { name: "object" };

// any类型会关闭类型检查
anything.foo.bar; // 不会报错，但可能运行时出错
```

### 2. unknown 类型
```typescript
let userInput: unknown;
userInput = 5;
userInput = "hello";

// 使用前需要类型检查
if (typeof userInput === "string") {
    console.log(userInput.toUpperCase()); // 安全
}
```

### 3. void 类型
```typescript
// 函数没有返回值
function logMessage(message: string): void {
    console.log(message);
}

// void类型的变量只能赋值undefined或null
let unusable: void = undefined;
```

### 4. null 和 undefined
```typescript
let nullValue: null = null;
let undefinedValue: undefined = undefined;

// 在严格模式下，null和undefined只能赋值给自己或void
let maybeString: string | null = null;
let maybeNumber: number | undefined = undefined;
```

### 5. never 类型
```typescript
// 永远不会返回的函数
function throwError(message: string): never {
    throw new Error(message);
}

// 无限循环
function infiniteLoop(): never {
    while (true) {
        // 无限循环
    }
}
```

## 🔗 联合类型和交叉类型

### 联合类型 (Union Types)
```typescript
// 可以是string或number
let id: string | number;
id = "ABC123";
id = 123;

// 函数参数的联合类型
function printId(id: string | number) {
    if (typeof id === "string") {
        console.log(id.toUpperCase());
    } else {
        console.log(id.toFixed(2));
    }
}

// 数组的联合类型
let mixedArray: (string | number)[] = ["hello", 42, "world"];
```

### 字面量类型
```typescript
// 字符串字面量类型
let direction: "up" | "down" | "left" | "right";
direction = "up"; // ✓
// direction = "forward"; // ✗ 错误

// 数字字面量类型
let diceRoll: 1 | 2 | 3 | 4 | 5 | 6;

// 布尔字面量类型
let success: true = true;
```

## 🤖 类型推断

TypeScript可以自动推断变量的类型：

```typescript
// 自动推断为number
let count = 42;

// 自动推断为string
let message = "Hello TypeScript";

// 自动推断为boolean
let isReady = true;

// 自动推断为number[]
let numbers = [1, 2, 3, 4, 5];

// 自动推断为(string | number)[]
let mixed = ["hello", 42];
```

## 🎯 类型断言

有时你比TypeScript更了解某个值的类型：

```typescript
// 方式1：尖括号语法
let someValue: any = "this is a string";
let strLength: number = (<string>someValue).length;

// 方式2：as语法（推荐，在JSX中只能用这种）
let someValue2: any = "this is a string";
let strLength2: number = (someValue2 as string).length;

// 实际应用示例
let userInput = document.getElementById("user-input") as HTMLInputElement;
userInput.value = "Hello";
```

## 📝 实践练习

创建 `03-basic-types/practice.ts`：

```typescript
// 练习1：定义个人信息
let studentName: string = "李明";
let studentAge: number = 20;
let isEnrolled: boolean = true;
let grades: number[] = [85, 92, 78, 96, 88];

// 练习2：使用元组表示坐标点
let point: [number, number] = [10, 20];
let [x, y] = point;

// 练习3：定义状态枚举
enum OrderStatus {
    Pending = "pending",
    Processing = "processing",
    Shipped = "shipped",
    Delivered = "delivered"
}

// 练习4：联合类型函数
function formatValue(value: string | number): string {
    if (typeof value === "string") {
        return value.toUpperCase();
    } else {
        return value.toString();
    }
}

// 测试
console.log(formatValue("hello")); // "HELLO"
console.log(formatValue(123));     // "123"
```

## 🎯 本章小结

本章学习了TypeScript的基本类型系统：
- 基本数据类型：number、string、boolean、array、tuple、enum
- 特殊类型：any、unknown、void、null、undefined、never
- 联合类型和字面量类型
- 类型推断和类型断言

掌握这些基本类型是学习TypeScript的基础，它们将在后续章节中频繁使用。

---

**下一章：[变量声明](../04-variables/README.md)**
