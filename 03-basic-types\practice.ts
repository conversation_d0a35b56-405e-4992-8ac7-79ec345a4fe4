// TypeScript 基本类型练习

// ===== 练习1：基本数据类型 =====
console.log("=== 练习1：基本数据类型 ===");

// 数字类型
let studentAge: number = 20;
let examScore: number = 95.5;
let hexColor: number = 0xFF0000; // 红色

// 字符串类型
let studentName: string = "李明";
let greeting: string = `你好，${studentName}！你今年${studentAge}岁了。`;

// 布尔类型
let isEnrolled: boolean = true;
let hasGraduated: boolean = false;

console.log(greeting);
console.log(`是否已注册: ${isEnrolled}`);

// ===== 练习2：数组和元组 =====
console.log("\n=== 练习2：数组和元组 ===");

// 数组类型
let grades: number[] = [85, 92, 78, 96, 88];
let subjects: Array<string> = ["数学", "英语", "物理", "化学"];

// 元组类型
let student: [string, number, boolean] = ["王小明", 19, true];
let coordinate: [number, number] = [10, 20];

// 解构元组
let [name, age, enrolled] = student;
let [x, y] = coordinate;

console.log(`学生信息: ${name}, ${age}岁, 已注册: ${enrolled}`);
console.log(`坐标点: (${x}, ${y})`);
console.log(`成绩: ${grades.join(", ")}`);
console.log(`科目: ${subjects.join(", ")}`);

// ===== 练习3：枚举类型 =====
console.log("\n=== 练习3：枚举类型 ===");

// 数字枚举
enum Grade {
    Freshman,  // 0
    Sophomore, // 1
    Junior,    // 2
    Senior     // 3
}

// 字符串枚举
enum OrderStatus {
    Pending = "pending",
    Processing = "processing", 
    Shipped = "shipped",
    Delivered = "delivered"
}

let currentGrade: Grade = Grade.Junior;
let orderStatus: OrderStatus = OrderStatus.Processing;

console.log(`当前年级: ${Grade[currentGrade]} (${currentGrade})`);
console.log(`订单状态: ${orderStatus}`);

// ===== 练习4：联合类型 =====
console.log("\n=== 练习4：联合类型 ===");

// 联合类型变量
let id: string | number;
id = "STU001";
console.log(`学号 (字符串): ${id}`);

id = 12345;
console.log(`学号 (数字): ${id}`);

// 联合类型函数
function formatValue(value: string | number): string {
    if (typeof value === "string") {
        return value.toUpperCase();
    } else {
        return value.toString().padStart(6, "0");
    }
}

console.log(`格式化 "hello": ${formatValue("hello")}`);
console.log(`格式化 123: ${formatValue(123)}`);

// ===== 练习5：字面量类型 =====
console.log("\n=== 练习5：字面量类型 ===");

// 字符串字面量类型
let direction: "up" | "down" | "left" | "right" = "up";
let theme: "light" | "dark" = "dark";

// 数字字面量类型
let diceRoll: 1 | 2 | 3 | 4 | 5 | 6 = 4;

console.log(`方向: ${direction}`);
console.log(`主题: ${theme}`);
console.log(`骰子点数: ${diceRoll}`);

// ===== 练习6：类型推断 =====
console.log("\n=== 练习6：类型推断 ===");

// TypeScript自动推断类型
let autoNumber = 42;        // 推断为 number
let autoString = "TypeScript"; // 推断为 string
let autoBoolean = true;     // 推断为 boolean
let autoArray = [1, 2, 3];  // 推断为 number[]
let autoMixed = ["hello", 42]; // 推断为 (string | number)[]

console.log(`自动推断的数字: ${autoNumber}`);
console.log(`自动推断的字符串: ${autoString}`);
console.log(`自动推断的布尔值: ${autoBoolean}`);
console.log(`自动推断的数组: [${autoArray.join(", ")}]`);
console.log(`自动推断的混合数组: [${autoMixed.join(", ")}]`);

// ===== 练习7：实际应用示例 =====
console.log("\n=== 练习7：实际应用示例 ===");

// 用户信息接口（预告下一章内容）
interface UserInfo {
    id: string | number;
    name: string;
    age: number;
    isActive: boolean;
    hobbies: string[];
    address: [string, string]; // [城市, 街道]
}

// 创建用户信息
let user: UserInfo = {
    id: "USER001",
    name: "张三",
    age: 25,
    isActive: true,
    hobbies: ["阅读", "游泳", "编程"],
    address: ["北京", "朝阳区"]
};

// 显示用户信息
function displayUserInfo(user: UserInfo): void {
    console.log(`用户ID: ${user.id}`);
    console.log(`姓名: ${user.name}`);
    console.log(`年龄: ${user.age}`);
    console.log(`状态: ${user.isActive ? "活跃" : "非活跃"}`);
    console.log(`爱好: ${user.hobbies.join(", ")}`);
    console.log(`地址: ${user.address[0]} ${user.address[1]}`);
}

displayUserInfo(user);

// ===== 练习8：类型断言 =====
console.log("\n=== 练习8：类型断言 ===");

// 模拟从API获取的数据
let apiResponse: any = {
    name: "产品名称",
    price: 99.99,
    inStock: true
};

// 使用类型断言
interface Product {
    name: string;
    price: number;
    inStock: boolean;
}

let product = apiResponse as Product;
console.log(`产品: ${product.name}, 价格: ¥${product.price}, 库存: ${product.inStock ? "有" : "无"}`);

console.log("\n🎉 基本类型练习完成！");
