# 第4章：变量声明

## 🎯 学习目标
- 理解var、let、const的区别
- 掌握变量作用域和提升
- 学会解构赋值
- 了解展开运算符的使用

## 📖 变量声明方式

TypeScript支持三种变量声明方式：`var`、`let`、`const`。

### 1. var 声明

```typescript
var message = "Hello";
var count = 10;

// var的特点：
// 1. 函数作用域
// 2. 变量提升
// 3. 可以重复声明
```

#### var的问题：
```typescript
function varProblem() {
    for (var i = 0; i < 3; i++) {
        setTimeout(() => {
            console.log(i); // 输出 3, 3, 3
        }, 100);
    }
}

// 变量提升问题
console.log(hoistedVar); // undefined（而不是报错）
var hoistedVar = "I'm hoisted";
```

### 2. let 声明

```typescript
let userName = "张三";
let userAge = 25;

// let的特点：
// 1. 块级作用域
// 2. 不能重复声明
// 3. 暂时性死区
```

#### let解决var的问题：
```typescript
function letSolution() {
    for (let i = 0; i < 3; i++) {
        setTimeout(() => {
            console.log(i); // 输出 0, 1, 2
        }, 100);
    }
}

// 块级作用域
if (true) {
    let blockScoped = "只在这个块中可见";
}
// console.log(blockScoped); // 错误：blockScoped未定义
```

### 3. const 声明

```typescript
const PI = 3.14159;
const APP_NAME = "我的应用";

// const的特点：
// 1. 块级作用域
// 2. 必须初始化
// 3. 不能重新赋值
// 4. 对象和数组的内容可以修改
```

#### const的使用：
```typescript
// 基本类型不能修改
const name = "李四";
// name = "王五"; // 错误：不能重新赋值

// 对象内容可以修改
const user = {
    name: "张三",
    age: 25
};

user.age = 26; // ✓ 可以修改属性
user.email = "<EMAIL>"; // ✓ 可以添加属性
// user = {}; // ✗ 不能重新赋值整个对象

// 数组内容可以修改
const numbers = [1, 2, 3];
numbers.push(4); // ✓ 可以修改数组内容
// numbers = []; // ✗ 不能重新赋值整个数组
```

## 🔍 作用域详解

### 1. 全局作用域
```typescript
// 全局变量
let globalVar = "我是全局变量";

function accessGlobal() {
    console.log(globalVar); // 可以访问
}
```

### 2. 函数作用域
```typescript
function functionScope() {
    var functionVar = "函数作用域";
    let functionLet = "函数let";
    const functionConst = "函数const";
    
    // 都只在函数内可见
}

// console.log(functionVar); // 错误：未定义
```

### 3. 块级作用域
```typescript
if (true) {
    var blockVar = "var在块中";
    let blockLet = "let在块中";
    const blockConst = "const在块中";
}

console.log(blockVar); // ✓ var可以访问
// console.log(blockLet); // ✗ let不能访问
// console.log(blockConst); // ✗ const不能访问
```

## 🎯 解构赋值

### 1. 数组解构
```typescript
// 基本数组解构
let [first, second, third] = [1, 2, 3];
console.log(first);  // 1
console.log(second); // 2
console.log(third);  // 3

// 跳过元素
let [a, , c] = [1, 2, 3];
console.log(a); // 1
console.log(c); // 3

// 默认值
let [x = 10, y = 20] = [1];
console.log(x); // 1
console.log(y); // 20

// 剩余元素
let [head, ...tail] = [1, 2, 3, 4, 5];
console.log(head); // 1
console.log(tail); // [2, 3, 4, 5]

// 交换变量
let num1 = 10;
let num2 = 20;
[num1, num2] = [num2, num1];
console.log(num1); // 20
console.log(num2); // 10
```

### 2. 对象解构
```typescript
// 基本对象解构
const person = {
    name: "张三",
    age: 25,
    city: "北京"
};

let { name, age, city } = person;
console.log(name); // "张三"
console.log(age);  // 25
console.log(city); // "北京"

// 重命名
let { name: personName, age: personAge } = person;
console.log(personName); // "张三"
console.log(personAge);  // 25

// 默认值
let { name: userName, country = "中国" } = person;
console.log(userName); // "张三"
console.log(country);  // "中国"

// 嵌套解构
const student = {
    info: {
        name: "李四",
        grade: "大三"
    },
    scores: [85, 92, 78]
};

let { info: { name: studentName, grade }, scores: [math, english] } = student;
console.log(studentName); // "李四"
console.log(grade);       // "大三"
console.log(math);        // 85
console.log(english);     // 92
```

### 3. 函数参数解构
```typescript
// 对象参数解构
interface UserConfig {
    name: string;
    age?: number;
    email?: string;
}

function createUser({ name, age = 18, email = "未提供" }: UserConfig) {
    console.log(`用户：${name}，年龄：${age}，邮箱：${email}`);
}

createUser({ name: "王五" });
createUser({ name: "赵六", age: 30, email: "<EMAIL>" });

// 数组参数解构
function processCoordinates([x, y]: [number, number]) {
    console.log(`坐标：(${x}, ${y})`);
}

processCoordinates([10, 20]);
```

## 🌟 展开运算符 (Spread Operator)

### 1. 数组展开
```typescript
const arr1 = [1, 2, 3];
const arr2 = [4, 5, 6];

// 合并数组
const combined = [...arr1, ...arr2];
console.log(combined); // [1, 2, 3, 4, 5, 6]

// 复制数组
const copied = [...arr1];
console.log(copied); // [1, 2, 3]

// 在数组中插入元素
const inserted = [0, ...arr1, 3.5, ...arr2, 7];
console.log(inserted); // [0, 1, 2, 3, 3.5, 4, 5, 6, 7]

// 函数调用
function sum(a: number, b: number, c: number) {
    return a + b + c;
}

const numbers = [1, 2, 3];
console.log(sum(...numbers)); // 6
```

### 2. 对象展开
```typescript
const baseUser = {
    name: "张三",
    age: 25
};

const extendedUser = {
    ...baseUser,
    email: "<EMAIL>",
    city: "上海"
};

console.log(extendedUser);
// { name: "张三", age: 25, email: "<EMAIL>", city: "上海" }

// 覆盖属性
const updatedUser = {
    ...baseUser,
    age: 26, // 覆盖原来的age
    isActive: true
};

console.log(updatedUser);
// { name: "张三", age: 26, isActive: true }
```

## 📝 最佳实践

### 1. 选择合适的声明方式
```typescript
// ✓ 推荐：优先使用const
const CONFIG = {
    API_URL: "https://api.example.com",
    TIMEOUT: 5000
};

// ✓ 推荐：需要重新赋值时使用let
let counter = 0;
for (let i = 0; i < 10; i++) {
    counter += i;
}

// ✗ 避免：不要使用var
// var oldStyle = "避免使用";
```

### 2. 使用有意义的变量名
```typescript
// ✗ 不好的命名
let d = new Date();
let u = users.filter(x => x.active);

// ✓ 好的命名
let currentDate = new Date();
let activeUsers = users.filter(user => user.isActive);
```

### 3. 合理使用解构
```typescript
// ✓ 简化代码
const { name, email } = user;
const [first, second] = coordinates;

// ✓ 函数参数解构
function updateUser({ id, name, email }: UserUpdate) {
    // 实现更新逻辑
}
```

## 🎯 实践练习

创建 `04-variables/practice.ts`：

```typescript
// 练习1：作用域理解
function scopeDemo() {
    if (true) {
        let blockVar = "块级变量";
        const blockConst = "块级常量";
        console.log(blockVar, blockConst);
    }
    // 尝试访问块级变量会报错
}

// 练习2：解构赋值
const userInfo = {
    id: 1,
    profile: {
        name: "李明",
        age: 28,
        address: {
            city: "深圳",
            district: "南山区"
        }
    },
    hobbies: ["阅读", "游泳", "编程"]
};

// 解构嵌套对象
const { 
    profile: { 
        name, 
        age, 
        address: { city } 
    }, 
    hobbies: [firstHobby, ...otherHobbies] 
} = userInfo;

console.log(`${name}, ${age}岁, 住在${city}, 爱好${firstHobby}等`);
```

## 🎯 本章小结

本章学习了TypeScript中的变量声明：
- `var`、`let`、`const`的区别和使用场景
- 作用域的概念和重要性
- 解构赋值的强大功能
- 展开运算符的实用性

推荐在现代TypeScript开发中优先使用`const`，需要重新赋值时使用`let`，避免使用`var`。

---

**下一章：[函数](../05-functions/README.md)**
