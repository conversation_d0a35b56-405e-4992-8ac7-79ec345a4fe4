# 第5章：函数

## 🎯 学习目标
- 掌握TypeScript中函数的定义和类型注解
- 理解可选参数、默认参数和剩余参数
- 学会函数重载的使用
- 了解箭头函数和this绑定

## 📖 函数基础

### 1. 函数声明
```typescript
// 基本函数声明
function greet(name: string): string {
    return `Hello, ${name}!`;
}

// 函数表达式
const add = function(x: number, y: number): number {
    return x + y;
};

// 箭头函数
const multiply = (x: number, y: number): number => {
    return x * y;
};

// 简化的箭头函数
const square = (x: number): number => x * x;
```

### 2. 函数类型
```typescript
// 定义函数类型
type MathOperation = (x: number, y: number) => number;

// 使用函数类型
const subtract: MathOperation = (x, y) => x - y;
const divide: MathOperation = (x, y) => x / y;

// 函数类型作为参数
function calculate(operation: MathOperation, a: number, b: number): number {
    return operation(a, b);
}

console.log(calculate(add, 10, 5));      // 15
console.log(calculate(subtract, 10, 5)); // 5
```

## 🔧 参数类型

### 1. 可选参数
```typescript
// 可选参数用 ? 标记
function buildName(firstName: string, lastName?: string): string {
    if (lastName) {
        return `${firstName} ${lastName}`;
    }
    return firstName;
}

console.log(buildName("张"));        // "张"
console.log(buildName("张", "三"));   // "张 三"

// 可选参数必须在必需参数之后
function createUser(name: string, age?: number, email?: string): void {
    console.log(`用户: ${name}, 年龄: ${age || "未知"}, 邮箱: ${email || "未提供"}`);
}
```

### 2. 默认参数
```typescript
// 默认参数
function greetUser(name: string, greeting: string = "你好"): string {
    return `${greeting}, ${name}!`;
}

console.log(greetUser("李四"));           // "你好, 李四!"
console.log(greetUser("王五", "欢迎"));    // "欢迎, 王五!"

// 默认参数可以是表达式
function createId(prefix: string = "USER", timestamp: number = Date.now()): string {
    return `${prefix}_${timestamp}`;
}
```

### 3. 剩余参数
```typescript
// 剩余参数
function sum(...numbers: number[]): number {
    return numbers.reduce((total, num) => total + num, 0);
}

console.log(sum(1, 2, 3));        // 6
console.log(sum(1, 2, 3, 4, 5));  // 15

// 混合使用
function introduce(name: string, ...hobbies: string[]): string {
    const hobbyList = hobbies.length > 0 ? hobbies.join(", ") : "无";
    return `我是${name}，我的爱好有：${hobbyList}`;
}

console.log(introduce("张三", "阅读", "游泳", "编程"));
```

## 🔄 函数重载

函数重载允许一个函数根据不同的参数类型或数量有不同的行为：

```typescript
// 重载签名
function processData(data: string): string;
function processData(data: number): number;
function processData(data: boolean): boolean;

// 实现签名
function processData(data: string | number | boolean): string | number | boolean {
    if (typeof data === "string") {
        return data.toUpperCase();
    } else if (typeof data === "number") {
        return data * 2;
    } else {
        return !data;
    }
}

// 使用
console.log(processData("hello"));  // "HELLO"
console.log(processData(5));        // 10
console.log(processData(true));     // false

// 更复杂的重载示例
function createElement(tag: "div"): HTMLDivElement;
function createElement(tag: "span"): HTMLSpanElement;
function createElement(tag: "input"): HTMLInputElement;
function createElement(tag: string): HTMLElement;

function createElement(tag: string): HTMLElement {
    return document.createElement(tag);
}
```

## 🎯 箭头函数和this

### 1. 箭头函数特点
```typescript
// 传统函数 vs 箭头函数
const obj = {
    name: "对象",
    
    // 传统函数方法
    traditionalMethod: function() {
        console.log(`传统函数中的this.name: ${this.name}`);
        
        // 内部函数中的this指向会改变
        setTimeout(function() {
            console.log(`setTimeout中的this.name: ${this.name}`); // undefined
        }, 100);
    },
    
    // 箭头函数方法
    arrowMethod: function() {
        console.log(`外层函数中的this.name: ${this.name}`);
        
        // 箭头函数保持外层的this
        setTimeout(() => {
            console.log(`箭头函数中的this.name: ${this.name}`); // "对象"
        }, 100);
    }
};
```

### 2. 类中的箭头函数
```typescript
class Counter {
    private count: number = 0;
    
    // 传统方法
    increment(): void {
        this.count++;
    }
    
    // 箭头函数方法（自动绑定this）
    incrementArrow = (): void => {
        this.count++;
    }
    
    getCount(): number {
        return this.count;
    }
}

const counter = new Counter();
const incrementFn = counter.incrementArrow;
incrementFn(); // this仍然指向counter实例
```

## 🔍 高级函数概念

### 1. 泛型函数
```typescript
// 泛型函数
function identity<T>(arg: T): T {
    return arg;
}

// 使用
let stringResult = identity<string>("hello");
let numberResult = identity<number>(42);
let booleanResult = identity(true); // 类型推断

// 泛型约束
interface Lengthwise {
    length: number;
}

function logLength<T extends Lengthwise>(arg: T): T {
    console.log(arg.length);
    return arg;
}

logLength("hello");     // ✓ 字符串有length属性
logLength([1, 2, 3]);   // ✓ 数组有length属性
// logLength(123);      // ✗ 数字没有length属性
```

### 2. 回调函数
```typescript
// 回调函数类型定义
type Callback<T> = (error: Error | null, result?: T) => void;

// 异步函数示例
function fetchData(url: string, callback: Callback<string>): void {
    // 模拟异步操作
    setTimeout(() => {
        if (url.startsWith("http")) {
            callback(null, `数据来自 ${url}`);
        } else {
            callback(new Error("无效的URL"));
        }
    }, 1000);
}

// 使用回调函数
fetchData("https://api.example.com", (error, result) => {
    if (error) {
        console.error("错误:", error.message);
    } else {
        console.log("成功:", result);
    }
});
```

### 3. 高阶函数
```typescript
// 高阶函数：接受函数作为参数或返回函数
function createMultiplier(factor: number): (x: number) => number {
    return (x: number) => x * factor;
}

const double = createMultiplier(2);
const triple = createMultiplier(3);

console.log(double(5));  // 10
console.log(triple(5));  // 15

// 函数装饰器
function timer<T extends (...args: any[]) => any>(fn: T): T {
    return ((...args: any[]) => {
        const start = Date.now();
        const result = fn(...args);
        const end = Date.now();
        console.log(`函数执行时间: ${end - start}ms`);
        return result;
    }) as T;
}

// 使用装饰器
const timedAdd = timer((a: number, b: number) => a + b);
timedAdd(1, 2); // 输出执行时间
```

## 📝 实践练习

创建 `05-functions/practice.ts`：

```typescript
// 练习1：计算器函数
function calculator(
    operation: "add" | "subtract" | "multiply" | "divide",
    a: number,
    b: number
): number {
    switch (operation) {
        case "add":
            return a + b;
        case "subtract":
            return a - b;
        case "multiply":
            return a * b;
        case "divide":
            if (b === 0) throw new Error("除数不能为零");
            return a / b;
        default:
            throw new Error("不支持的操作");
    }
}

// 练习2：数组处理函数
function processArray<T>(
    array: T[],
    processor: (item: T, index: number) => T
): T[] {
    return array.map(processor);
}

// 使用示例
const numbers = [1, 2, 3, 4, 5];
const doubled = processArray(numbers, (num) => num * 2);
console.log(doubled); // [2, 4, 6, 8, 10]
```

## 🎯 本章小结

本章学习了TypeScript中函数的核心概念：
- 函数声明和类型注解
- 可选参数、默认参数、剩余参数
- 函数重载
- 箭头函数和this绑定
- 泛型函数和高阶函数

函数是TypeScript编程的基础，掌握这些概念对后续学习非常重要。

---

**下一章：[接口](../06-interfaces/README.md)**
