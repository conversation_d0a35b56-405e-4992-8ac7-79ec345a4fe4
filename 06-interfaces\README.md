# 第6章：接口

## 🎯 学习目标
- 理解接口的概念和作用
- 掌握接口的定义和使用
- 学会可选属性、只读属性的使用
- 了解接口继承和函数接口

## 📖 接口基础

接口（Interface）是TypeScript的核心特性之一，用于定义对象的结构和契约。

### 1. 基本接口定义
```typescript
// 定义用户接口
interface User {
    id: number;
    name: string;
    email: string;
    age: number;
}

// 使用接口
const user: User = {
    id: 1,
    name: "张三",
    email: "<EMAIL>",
    age: 25
};

// 函数参数使用接口
function displayUser(user: User): void {
    console.log(`用户：${user.name}，邮箱：${user.email}`);
}

displayUser(user);
```

### 2. 可选属性
```typescript
interface Product {
    id: number;
    name: string;
    price: number;
    description?: string;  // 可选属性
    category?: string;     // 可选属性
}

// 可以不提供可选属性
const product1: Product = {
    id: 1,
    name: "笔记本电脑",
    price: 5999
};

// 也可以提供可选属性
const product2: Product = {
    id: 2,
    name: "手机",
    price: 3999,
    description: "最新款智能手机",
    category: "电子产品"
};
```

### 3. 只读属性
```typescript
interface Config {
    readonly apiUrl: string;
    readonly timeout: number;
    retryCount: number;  // 可修改
}

const config: Config = {
    apiUrl: "https://api.example.com",
    timeout: 5000,
    retryCount: 3
};

// config.apiUrl = "新URL";  // 错误：只读属性不能修改
config.retryCount = 5;       // ✓ 可以修改非只读属性

// 只读数组
interface ReadonlyStringArray {
    readonly [index: number]: string;
}

const myArray: ReadonlyStringArray = ["a", "b", "c"];
// myArray[0] = "x"; // 错误：只读
```

## 🔧 高级接口特性

### 1. 索引签名
```typescript
// 字符串索引签名
interface StringDictionary {
    [key: string]: string;
}

const dict: StringDictionary = {
    name: "张三",
    city: "北京",
    country: "中国"
};

// 数字索引签名
interface NumberArray {
    [index: number]: number;
}

const numbers: NumberArray = [1, 2, 3, 4, 5];

// 混合索引签名
interface MixedDictionary {
    [key: string]: string | number;
    length: number;  // 必须符合索引签名的类型
}
```

### 2. 函数接口
```typescript
// 函数接口
interface SearchFunc {
    (source: string, subString: string): boolean;
}

// 实现函数接口
const mySearch: SearchFunc = function(source, subString) {
    return source.indexOf(subString) > -1;
};

// 箭头函数实现
const mySearch2: SearchFunc = (source, subString) => {
    return source.includes(subString);
};

// 带属性的函数接口
interface Counter {
    (start: number): string;
    interval: number;
    reset(): void;
}

function getCounter(): Counter {
    let counter = function(start: number) {
        return `计数从 ${start} 开始`;
    } as Counter;
    
    counter.interval = 123;
    counter.reset = function() {
        console.log("重置计数器");
    };
    
    return counter;
}
```

### 3. 类接口
```typescript
// 类实现接口
interface Flyable {
    fly(): void;
    altitude: number;
}

interface Swimmable {
    swim(): void;
    depth: number;
}

// 类实现单个接口
class Bird implements Flyable {
    altitude: number = 0;
    
    fly(): void {
        this.altitude = 100;
        console.log(`鸟儿飞到了 ${this.altitude} 米高度`);
    }
}

// 类实现多个接口
class Duck implements Flyable, Swimmable {
    altitude: number = 0;
    depth: number = 0;
    
    fly(): void {
        this.altitude = 50;
        console.log(`鸭子飞到了 ${this.altitude} 米高度`);
    }
    
    swim(): void {
        this.depth = 2;
        console.log(`鸭子游到了 ${this.depth} 米深度`);
    }
}
```

## 🔗 接口继承

### 1. 基本继承
```typescript
// 基础接口
interface Animal {
    name: string;
    age: number;
}

// 继承接口
interface Dog extends Animal {
    breed: string;
    bark(): void;
}

// 使用继承的接口
const myDog: Dog = {
    name: "旺财",
    age: 3,
    breed: "金毛",
    bark() {
        console.log("汪汪汪！");
    }
};
```

### 2. 多重继承
```typescript
interface Walkable {
    walk(): void;
}

interface Runnable {
    run(): void;
}

// 多重继承
interface Athlete extends Walkable, Runnable {
    name: string;
    sport: string;
}

const athlete: Athlete = {
    name: "李明",
    sport: "田径",
    walk() {
        console.log("正在走路");
    },
    run() {
        console.log("正在跑步");
    }
};
```

### 3. 接口合并
```typescript
// 同名接口会自动合并
interface User {
    name: string;
}

interface User {
    age: number;
}

interface User {
    email: string;
}

// 合并后的User接口包含所有属性
const user: User = {
    name: "张三",
    age: 25,
    email: "<EMAIL>"
};
```

## 🎨 实际应用示例

### 1. API响应接口
```typescript
// API响应基础接口
interface ApiResponse<T> {
    success: boolean;
    message: string;
    data?: T;
    timestamp: number;
}

// 用户数据接口
interface UserData {
    id: number;
    username: string;
    profile: {
        firstName: string;
        lastName: string;
        avatar?: string;
    };
}

// 使用泛型接口
type UserResponse = ApiResponse<UserData>;
type UserListResponse = ApiResponse<UserData[]>;

// 模拟API响应
const userResponse: UserResponse = {
    success: true,
    message: "获取用户信息成功",
    data: {
        id: 1,
        username: "zhangsan",
        profile: {
            firstName: "三",
            lastName: "张"
        }
    },
    timestamp: Date.now()
};
```

### 2. 配置接口
```typescript
// 应用配置接口
interface AppConfig {
    readonly app: {
        name: string;
        version: string;
        debug: boolean;
    };
    database: {
        host: string;
        port: number;
        username: string;
        password: string;
    };
    features: {
        [featureName: string]: boolean;
    };
}

const config: AppConfig = {
    app: {
        name: "我的应用",
        version: "1.0.0",
        debug: true
    },
    database: {
        host: "localhost",
        port: 3306,
        username: "admin",
        password: "password"
    },
    features: {
        userRegistration: true,
        emailNotification: false,
        darkMode: true
    }
};
```

### 3. 事件处理接口
```typescript
// 事件接口
interface Event {
    type: string;
    timestamp: number;
}

interface ClickEvent extends Event {
    type: "click";
    target: string;
    coordinates: {
        x: number;
        y: number;
    };
}

interface KeyboardEvent extends Event {
    type: "keydown" | "keyup";
    key: string;
    ctrlKey: boolean;
    shiftKey: boolean;
}

// 事件处理器接口
interface EventHandler<T extends Event> {
    handle(event: T): void;
}

// 实现事件处理器
class ClickHandler implements EventHandler<ClickEvent> {
    handle(event: ClickEvent): void {
        console.log(`点击事件：目标 ${event.target}，坐标 (${event.coordinates.x}, ${event.coordinates.y})`);
    }
}
```

## 📝 最佳实践

### 1. 接口命名
```typescript
// ✓ 好的命名
interface User { }
interface UserService { }
interface ApiResponse<T> { }

// ✗ 避免的命名
interface IUser { }  // 避免I前缀
interface userType { } // 避免小写开头
```

### 2. 接口组织
```typescript
// 将相关接口组织在一起
namespace UserManagement {
    export interface User {
        id: number;
        name: string;
    }
    
    export interface UserService {
        getUser(id: number): User;
        createUser(user: Omit<User, 'id'>): User;
    }
    
    export interface UserRepository {
        save(user: User): void;
        findById(id: number): User | null;
    }
}
```

## 🎯 本章小结

接口是TypeScript中定义对象结构的强大工具：
- 定义对象的形状和契约
- 支持可选属性和只读属性
- 可以描述函数和类的结构
- 支持继承和合并
- 提供了灵活的类型约束机制

接口是构建大型应用程序的基础，有助于代码的可维护性和团队协作。

---

**下一章：[类](../07-classes/README.md)**
