# 第7章：类

## 🎯 学习目标
- 掌握TypeScript中类的定义和使用
- 理解访问修饰符和属性
- 学会继承和多态
- 了解抽象类和静态成员

## 📖 类基础

### 1. 基本类定义
```typescript
// 基本类定义
class Person {
    // 属性
    name: string;
    age: number;
    
    // 构造函数
    constructor(name: string, age: number) {
        this.name = name;
        this.age = age;
    }
    
    // 方法
    greet(): string {
        return `你好，我是${this.name}，今年${this.age}岁`;
    }
    
    // 方法
    celebrateBirthday(): void {
        this.age++;
        console.log(`生日快乐！现在我${this.age}岁了`);
    }
}

// 创建实例
const person = new Person("张三", 25);
console.log(person.greet());
person.celebrateBirthday();
```

### 2. 访问修饰符
```typescript
class BankAccount {
    public accountNumber: string;    // 公共属性（默认）
    private balance: number;         // 私有属性
    protected owner: string;         // 受保护属性
    
    constructor(accountNumber: string, owner: string, initialBalance: number = 0) {
        this.accountNumber = accountNumber;
        this.owner = owner;
        this.balance = initialBalance;
    }
    
    // 公共方法
    public getBalance(): number {
        return this.balance;
    }
    
    // 公共方法
    public deposit(amount: number): void {
        if (amount > 0) {
            this.balance += amount;
            console.log(`存入 ¥${amount}，余额：¥${this.balance}`);
        }
    }
    
    // 私有方法
    private validateAmount(amount: number): boolean {
        return amount > 0 && amount <= this.balance;
    }
    
    // 公共方法调用私有方法
    public withdraw(amount: number): boolean {
        if (this.validateAmount(amount)) {
            this.balance -= amount;
            console.log(`取出 ¥${amount}，余额：¥${this.balance}`);
            return true;
        }
        console.log("取款失败：金额无效或余额不足");
        return false;
    }
}

const account = new BankAccount("123456", "李四", 1000);
account.deposit(500);
account.withdraw(200);
// console.log(account.balance); // 错误：私有属性不能访问
console.log(account.getBalance()); // ✓ 通过公共方法访问
```

### 3. 参数属性
```typescript
// 参数属性简化写法
class Student {
    constructor(
        public name: string,           // 自动创建公共属性
        private studentId: string,     // 自动创建私有属性
        protected grade: number        // 自动创建受保护属性
    ) {
        // 构造函数体可以为空，属性已自动创建和赋值
    }
    
    getInfo(): string {
        return `学生：${this.name}，学号：${this.studentId}，年级：${this.grade}`;
    }
}

const student = new Student("王五", "2023001", 3);
console.log(student.getInfo());
console.log(student.name); // ✓ 公共属性可访问
// console.log(student.studentId); // 错误：私有属性不能访问
```

## 🔗 继承

### 1. 基本继承
```typescript
// 基类
class Animal {
    protected name: string;
    
    constructor(name: string) {
        this.name = name;
    }
    
    move(distance: number = 0): void {
        console.log(`${this.name} 移动了 ${distance} 米`);
    }
    
    makeSound(): void {
        console.log(`${this.name} 发出声音`);
    }
}

// 派生类
class Dog extends Animal {
    private breed: string;
    
    constructor(name: string, breed: string) {
        super(name); // 调用父类构造函数
        this.breed = breed;
    }
    
    // 重写父类方法
    makeSound(): void {
        console.log(`${this.name} 汪汪叫`);
    }
    
    // 新增方法
    wagTail(): void {
        console.log(`${this.name} 摇尾巴`);
    }
    
    getBreed(): string {
        return this.breed;
    }
}

class Cat extends Animal {
    constructor(name: string) {
        super(name);
    }
    
    // 重写父类方法
    makeSound(): void {
        console.log(`${this.name} 喵喵叫`);
    }
    
    // 新增方法
    climb(): void {
        console.log(`${this.name} 爬树`);
    }
}

// 使用继承
const dog = new Dog("旺财", "金毛");
const cat = new Cat("咪咪");

dog.move(10);
dog.makeSound();
dog.wagTail();

cat.move(5);
cat.makeSound();
cat.climb();
```

### 2. 多态
```typescript
// 多态示例
function makeAnimalSound(animal: Animal): void {
    animal.makeSound(); // 运行时决定调用哪个版本的方法
}

const animals: Animal[] = [
    new Dog("小黄", "拉布拉多"),
    new Cat("小白"),
    new Dog("小黑", "哈士奇")
];

// 多态调用
animals.forEach(animal => {
    makeAnimalSound(animal);
});
```

## 🎯 抽象类

```typescript
// 抽象类
abstract class Shape {
    protected color: string;
    
    constructor(color: string) {
        this.color = color;
    }
    
    // 抽象方法（必须在派生类中实现）
    abstract calculateArea(): number;
    abstract getPerimeter(): number;
    
    // 具体方法（可以在派生类中使用）
    getColor(): string {
        return this.color;
    }
    
    // 具体方法
    displayInfo(): void {
        console.log(`形状颜色：${this.color}，面积：${this.calculateArea()}`);
    }
}

// 实现抽象类
class Circle extends Shape {
    private radius: number;
    
    constructor(color: string, radius: number) {
        super(color);
        this.radius = radius;
    }
    
    // 实现抽象方法
    calculateArea(): number {
        return Math.PI * this.radius * this.radius;
    }
    
    getPerimeter(): number {
        return 2 * Math.PI * this.radius;
    }
}

class Rectangle extends Shape {
    private width: number;
    private height: number;
    
    constructor(color: string, width: number, height: number) {
        super(color);
        this.width = width;
        this.height = height;
    }
    
    // 实现抽象方法
    calculateArea(): number {
        return this.width * this.height;
    }
    
    getPerimeter(): number {
        return 2 * (this.width + this.height);
    }
}

// 使用抽象类
const circle = new Circle("红色", 5);
const rectangle = new Rectangle("蓝色", 10, 8);

circle.displayInfo();
rectangle.displayInfo();

// const shape = new Shape("绿色"); // 错误：不能实例化抽象类
```

## ⚡ 静态成员

```typescript
class MathUtils {
    // 静态属性
    static readonly PI = 3.14159;
    static readonly E = 2.71828;
    
    // 静态方法
    static add(a: number, b: number): number {
        return a + b;
    }
    
    static multiply(a: number, b: number): number {
        return a * b;
    }
    
    static circleArea(radius: number): number {
        return MathUtils.PI * radius * radius;
    }
    
    // 实例方法
    formatNumber(num: number): string {
        return num.toFixed(2);
    }
}

// 使用静态成员（不需要实例化）
console.log(MathUtils.PI);
console.log(MathUtils.add(5, 3));
console.log(MathUtils.circleArea(10));

// 使用实例方法（需要实例化）
const mathUtils = new MathUtils();
console.log(mathUtils.formatNumber(3.14159));
```

## 🔧 Getter和Setter

```typescript
class Temperature {
    private _celsius: number = 0;
    
    // Getter
    get celsius(): number {
        return this._celsius;
    }
    
    // Setter
    set celsius(value: number) {
        if (value < -273.15) {
            throw new Error("温度不能低于绝对零度");
        }
        this._celsius = value;
    }
    
    // 计算属性
    get fahrenheit(): number {
        return (this._celsius * 9/5) + 32;
    }
    
    set fahrenheit(value: number) {
        this._celsius = (value - 32) * 5/9;
    }
    
    get kelvin(): number {
        return this._celsius + 273.15;
    }
}

const temp = new Temperature();
temp.celsius = 25;
console.log(`摄氏度：${temp.celsius}°C`);
console.log(`华氏度：${temp.fahrenheit}°F`);
console.log(`开尔文：${temp.kelvin}K`);

temp.fahrenheit = 100;
console.log(`设置华氏度100°F后，摄氏度：${temp.celsius}°C`);
```

## 📝 实际应用示例

```typescript
// 用户管理系统示例
abstract class User {
    protected id: number;
    protected username: string;
    protected email: string;
    protected createdAt: Date;
    
    constructor(id: number, username: string, email: string) {
        this.id = id;
        this.username = username;
        this.email = email;
        this.createdAt = new Date();
    }
    
    // 抽象方法
    abstract getPermissions(): string[];
    abstract canAccess(resource: string): boolean;
    
    // 具体方法
    getProfile(): object {
        return {
            id: this.id,
            username: this.username,
            email: this.email,
            createdAt: this.createdAt
        };
    }
}

class RegularUser extends User {
    getPermissions(): string[] {
        return ["read"];
    }
    
    canAccess(resource: string): boolean {
        return resource === "profile" || resource === "dashboard";
    }
}

class AdminUser extends User {
    getPermissions(): string[] {
        return ["read", "write", "delete", "admin"];
    }
    
    canAccess(resource: string): boolean {
        return true; // 管理员可以访问所有资源
    }
    
    deleteUser(userId: number): void {
        console.log(`管理员删除了用户 ${userId}`);
    }
}

// 使用
const regularUser = new RegularUser(1, "user1", "<EMAIL>");
const adminUser = new AdminUser(2, "admin", "<EMAIL>");

console.log(regularUser.canAccess("profile")); // true
console.log(regularUser.canAccess("admin"));   // false
console.log(adminUser.canAccess("admin"));     // true
```

## 🎯 本章小结

TypeScript中的类提供了强大的面向对象编程能力：
- 类的定义和实例化
- 访问修饰符控制封装
- 继承实现代码复用
- 抽象类定义契约
- 静态成员提供工具方法
- Getter/Setter控制属性访问

类是构建复杂应用程序的重要工具，结合接口使用可以创建灵活且类型安全的代码结构。

---

**下一章：[泛型](../08-generics/README.md)**
