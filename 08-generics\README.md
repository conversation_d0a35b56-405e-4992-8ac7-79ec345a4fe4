# 第8章：泛型

## 🎯 学习目标
- 理解泛型的概念和作用
- 掌握泛型函数、接口、类的使用
- 学会泛型约束和条件类型
- 了解实用工具类型

## 📖 泛型基础

泛型（Generics）允许我们创建可重用的组件，这些组件可以支持多种类型而不是单一类型。

### 1. 为什么需要泛型？

```typescript
// 没有泛型的问题
function identityNumber(arg: number): number {
    return arg;
}

function identityString(arg: string): string {
    return arg;
}

function identityBoolean(arg: boolean): boolean {
    return arg;
}

// 使用any失去类型安全
function identityAny(arg: any): any {
    return arg;
}

// 泛型解决方案
function identity<T>(arg: T): T {
    return arg;
}

// 使用泛型函数
let numberResult = identity<number>(42);     // 类型：number
let stringResult = identity<string>("hello"); // 类型：string
let booleanResult = identity(true);          // 类型推断：boolean
```

### 2. 泛型函数
```typescript
// 基本泛型函数
function swap<T, U>(tuple: [T, U]): [U, T] {
    return [tuple[1], tuple[0]];
}

let swapped = swap<string, number>(["hello", 42]);
console.log(swapped); // [42, "hello"]

// 泛型数组操作
function getFirst<T>(array: T[]): T | undefined {
    return array.length > 0 ? array[0] : undefined;
}

let firstNumber = getFirst([1, 2, 3]);      // number | undefined
let firstString = getFirst(["a", "b", "c"]); // string | undefined

// 多个泛型参数
function merge<T, U>(obj1: T, obj2: U): T & U {
    return { ...obj1, ...obj2 };
}

let merged = merge({ name: "张三" }, { age: 25 });
console.log(merged.name); // "张三"
console.log(merged.age);  // 25
```

## 🔧 泛型接口

```typescript
// 泛型接口
interface Container<T> {
    value: T;
    getValue(): T;
    setValue(value: T): void;
}

// 实现泛型接口
class Box<T> implements Container<T> {
    constructor(private _value: T) {}
    
    getValue(): T {
        return this._value;
    }
    
    setValue(value: T): void {
        this._value = value;
    }
    
    get value(): T {
        return this._value;
    }
}

// 使用
const numberBox = new Box<number>(42);
const stringBox = new Box<string>("hello");

console.log(numberBox.getValue()); // 42
stringBox.setValue("world");
console.log(stringBox.value);      // "world"

// 泛型函数接口
interface Processor<T, U> {
    (input: T): U;
}

const stringToNumber: Processor<string, number> = (str) => {
    return parseInt(str, 10);
};

const numberToString: Processor<number, string> = (num) => {
    return num.toString();
};
```

## 🏗️ 泛型类

```typescript
// 泛型类
class Stack<T> {
    private items: T[] = [];
    
    push(item: T): void {
        this.items.push(item);
    }
    
    pop(): T | undefined {
        return this.items.pop();
    }
    
    peek(): T | undefined {
        return this.items[this.items.length - 1];
    }
    
    isEmpty(): boolean {
        return this.items.length === 0;
    }
    
    size(): number {
        return this.items.length;
    }
    
    toArray(): T[] {
        return [...this.items];
    }
}

// 使用泛型类
const numberStack = new Stack<number>();
numberStack.push(1);
numberStack.push(2);
numberStack.push(3);

console.log(numberStack.peek()); // 3
console.log(numberStack.pop());  // 3
console.log(numberStack.size()); // 2

const stringStack = new Stack<string>();
stringStack.push("hello");
stringStack.push("world");
console.log(stringStack.toArray()); // ["hello", "world"]
```

## 🔒 泛型约束

```typescript
// 基本泛型约束
interface Lengthwise {
    length: number;
}

function logLength<T extends Lengthwise>(arg: T): T {
    console.log(arg.length); // 现在可以访问length属性
    return arg;
}

logLength("hello");        // ✓ 字符串有length属性
logLength([1, 2, 3]);      // ✓ 数组有length属性
logLength({ length: 10 }); // ✓ 对象有length属性
// logLength(123);         // ✗ 数字没有length属性

// 使用keyof约束
function getProperty<T, K extends keyof T>(obj: T, key: K): T[K] {
    return obj[key];
}

const person = { name: "张三", age: 25, city: "北京" };
let name = getProperty(person, "name");     // string
let age = getProperty(person, "age");       // number
// let invalid = getProperty(person, "invalid"); // 错误：参数不存在

// 条件约束
function processArray<T extends any[]>(arr: T): T {
    console.log(`数组长度：${arr.length}`);
    return arr;
}

processArray([1, 2, 3]);        // ✓
processArray(["a", "b"]);       // ✓
// processArray("hello");       // ✗ 字符串不是数组
```

## 🎯 高级泛型概念

### 1. 条件类型
```typescript
// 条件类型
type IsArray<T> = T extends any[] ? true : false;

type Test1 = IsArray<string[]>;  // true
type Test2 = IsArray<number>;    // false

// 实用的条件类型
type NonNullable<T> = T extends null | undefined ? never : T;

type Test3 = NonNullable<string | null>;      // string
type Test4 = NonNullable<number | undefined>; // number

// 提取函数返回类型
type ReturnType<T> = T extends (...args: any[]) => infer R ? R : never;

function getString(): string { return "hello"; }
function getNumber(): number { return 42; }

type StringReturn = ReturnType<typeof getString>; // string
type NumberReturn = ReturnType<typeof getNumber>; // number
```

### 2. 映射类型
```typescript
// 基本映射类型
type Readonly<T> = {
    readonly [P in keyof T]: T[P];
};

type Partial<T> = {
    [P in keyof T]?: T[P];
};

interface User {
    id: number;
    name: string;
    email: string;
}

type ReadonlyUser = Readonly<User>;
// {
//     readonly id: number;
//     readonly name: string;
//     readonly email: string;
// }

type PartialUser = Partial<User>;
// {
//     id?: number;
//     name?: string;
//     email?: string;
// }

// 自定义映射类型
type Stringify<T> = {
    [P in keyof T]: string;
};

type StringifiedUser = Stringify<User>;
// {
//     id: string;
//     name: string;
//     email: string;
// }
```

## 🛠️ 实用工具类型

```typescript
// Pick - 选择特定属性
type UserBasic = Pick<User, "name" | "email">;
// {
//     name: string;
//     email: string;
// }

// Omit - 排除特定属性
type UserWithoutId = Omit<User, "id">;
// {
//     name: string;
//     email: string;
// }

// Record - 创建记录类型
type UserRoles = Record<string, string>;
// { [key: string]: string }

type SpecificRoles = Record<"admin" | "user" | "guest", boolean>;
// {
//     admin: boolean;
//     user: boolean;
//     guest: boolean;
// }

// Exclude - 从联合类型中排除
type T1 = Exclude<"a" | "b" | "c", "a">;        // "b" | "c"
type T2 = Exclude<string | number | boolean, string>; // number | boolean

// Extract - 从联合类型中提取
type T3 = Extract<"a" | "b" | "c", "a" | "f">;  // "a"
type T4 = Extract<string | number | boolean, string>; // string
```

## 📝 实际应用示例

```typescript
// API响应处理
interface ApiResponse<T> {
    success: boolean;
    data: T;
    message: string;
    timestamp: number;
}

interface PaginatedResponse<T> extends ApiResponse<T[]> {
    pagination: {
        page: number;
        limit: number;
        total: number;
    };
}

// 数据仓库模式
interface Repository<T, ID> {
    findById(id: ID): Promise<T | null>;
    findAll(): Promise<T[]>;
    save(entity: T): Promise<T>;
    delete(id: ID): Promise<boolean>;
}

class UserRepository implements Repository<User, number> {
    async findById(id: number): Promise<User | null> {
        // 实现查找逻辑
        return null;
    }
    
    async findAll(): Promise<User[]> {
        // 实现查找所有逻辑
        return [];
    }
    
    async save(user: User): Promise<User> {
        // 实现保存逻辑
        return user;
    }
    
    async delete(id: number): Promise<boolean> {
        // 实现删除逻辑
        return true;
    }
}

// 事件系统
interface EventMap {
    "user:created": { user: User };
    "user:updated": { user: User; changes: Partial<User> };
    "user:deleted": { userId: number };
}

class EventEmitter<T extends Record<string, any>> {
    private listeners: { [K in keyof T]?: Array<(data: T[K]) => void> } = {};
    
    on<K extends keyof T>(event: K, listener: (data: T[K]) => void): void {
        if (!this.listeners[event]) {
            this.listeners[event] = [];
        }
        this.listeners[event]!.push(listener);
    }
    
    emit<K extends keyof T>(event: K, data: T[K]): void {
        const eventListeners = this.listeners[event];
        if (eventListeners) {
            eventListeners.forEach(listener => listener(data));
        }
    }
}

// 使用事件系统
const emitter = new EventEmitter<EventMap>();

emitter.on("user:created", (data) => {
    console.log("用户创建:", data.user.name);
});

emitter.emit("user:created", {
    user: { id: 1, name: "张三", email: "<EMAIL>" }
});
```

## 🎯 本章小结

泛型是TypeScript中最强大的特性之一：
- 提供类型安全的同时保持代码复用性
- 支持函数、接口、类的泛型化
- 通过约束控制泛型的使用范围
- 条件类型和映射类型提供高级类型操作
- 实用工具类型简化常见类型操作

掌握泛型对于编写高质量、可维护的TypeScript代码至关重要。

---

**下一章：[模块](../09-modules/README.md)**
