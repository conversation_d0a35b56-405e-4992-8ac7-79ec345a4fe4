# 第9章：模块

## 🎯 学习目标
- 理解模块系统的概念和作用
- 掌握ES6模块的导入导出语法
- 学会模块的组织和管理
- 了解CommonJS和ES模块的区别

## 📖 模块基础

模块是TypeScript中组织代码的基本单位，每个文件都是一个模块。

### 1. 导出 (Export)

```typescript
// math.ts - 数学工具模块

// 导出变量
export const PI = 3.14159;
export const E = 2.71828;

// 导出函数
export function add(a: number, b: number): number {
    return a + b;
}

export function multiply(a: number, b: number): number {
    return a * b;
}

// 导出类
export class Calculator {
    add(a: number, b: number): number {
        return a + b;
    }
    
    subtract(a: number, b: number): number {
        return a - b;
    }
}

// 导出接口
export interface MathOperation {
    (a: number, b: number): number;
}

// 导出类型别名
export type OperationType = "add" | "subtract" | "multiply" | "divide";
```

### 2. 导入 (Import)

```typescript
// main.ts - 使用数学工具模块

// 导入具体的导出项
import { PI, add, Calculator } from './math';

console.log(PI); // 3.14159
console.log(add(5, 3)); // 8

const calc = new Calculator();
console.log(calc.add(10, 20)); // 30

// 导入并重命名
import { multiply as mult, E as eulerNumber } from './math';

console.log(mult(4, 5)); // 20
console.log(eulerNumber); // 2.71828

// 导入所有导出项
import * as MathUtils from './math';

console.log(MathUtils.PI);
console.log(MathUtils.add(1, 2));
const calculator = new MathUtils.Calculator();
```

### 3. 默认导出

```typescript
// user.ts - 用户模块

// 默认导出类
export default class User {
    constructor(
        public name: string,
        public email: string
    ) {}
    
    getInfo(): string {
        return `${this.name} (${this.email})`;
    }
}

// 也可以这样写
class User2 {
    constructor(public name: string) {}
}

export default User2;

// 或者导出函数作为默认
export default function createUser(name: string, email: string): User {
    return new User(name, email);
}
```

```typescript
// 导入默认导出
import User from './user';
import createUser from './user'; // 如果导出的是函数

// 可以给默认导入任意命名
import MyUser from './user';
import UserClass from './user';

const user = new User("张三", "<EMAIL>");
console.log(user.getInfo());
```

## 🔧 混合导出

```typescript
// utils.ts - 工具模块

// 默认导出
export default class Logger {
    log(message: string): void {
        console.log(`[LOG] ${message}`);
    }
}

// 命名导出
export const VERSION = "1.0.0";

export function formatDate(date: Date): string {
    return date.toISOString().split('T')[0];
}

export interface Config {
    apiUrl: string;
    timeout: number;
}

// 重新导出
export { PI, E } from './math';
```

```typescript
// 使用混合导出
import Logger, { VERSION, formatDate, Config } from './utils';

// 或者分开导入
import Logger from './utils';
import { VERSION, formatDate } from './utils';

const logger = new Logger();
logger.log(`应用版本: ${VERSION}`);
console.log(formatDate(new Date()));
```

## 📁 模块组织结构

### 1. 按功能组织

```
src/
├── models/
│   ├── user.ts
│   ├── product.ts
│   └── index.ts
├── services/
│   ├── userService.ts
│   ├── productService.ts
│   └── index.ts
├── utils/
│   ├── math.ts
│   ├── string.ts
│   └── index.ts
└── main.ts
```

### 2. 索引文件 (index.ts)

```typescript
// models/index.ts - 模型索引文件
export { default as User } from './user';
export { default as Product } from './product';
export * from './user';
export * from './product';

// 或者重新导出并重命名
export { User as UserModel } from './user';
export { Product as ProductModel } from './product';
```

```typescript
// 使用索引文件简化导入
import { User, Product } from './models';
// 而不是
// import { User } from './models/user';
// import { Product } from './models/product';
```

### 3. 桶导出模式

```typescript
// services/index.ts
export * from './userService';
export * from './productService';
export * from './authService';

// 这样可以从一个地方导入所有服务
import { UserService, ProductService, AuthService } from './services';
```

## 🔄 模块解析

### 1. 相对导入 vs 非相对导入

```typescript
// 相对导入 - 相对于当前文件
import { User } from './models/user';        // 同级目录
import { Config } from '../config/app';     // 上级目录
import { Utils } from '../../utils/index';  // 上两级目录

// 非相对导入 - 从node_modules或基础路径解析
import * as React from 'react';
import { Observable } from 'rxjs';
import { MyLibrary } from 'my-custom-library';
```

### 2. 路径映射 (tsconfig.json)

```json
{
  "compilerOptions": {
    "baseUrl": "./src",
    "paths": {
      "@models/*": ["models/*"],
      "@services/*": ["services/*"],
      "@utils/*": ["utils/*"],
      "@/*": ["*"]
    }
  }
}
```

```typescript
// 使用路径映射
import { User } from '@models/user';
import { UserService } from '@services/userService';
import { formatDate } from '@utils/date';
```

## 🌟 高级模块概念

### 1. 动态导入

```typescript
// 动态导入 - 运行时加载模块
async function loadMathModule() {
    const mathModule = await import('./math');
    console.log(mathModule.PI);
    return mathModule;
}

// 条件导入
async function loadUserInterface() {
    if (process.env.NODE_ENV === 'development') {
        const devModule = await import('./dev-tools');
        return devModule.default;
    } else {
        const prodModule = await import('./prod-tools');
        return prodModule.default;
    }
}

// 懒加载组件
async function loadComponent(componentName: string) {
    switch (componentName) {
        case 'UserList':
            const { UserList } = await import('./components/UserList');
            return UserList;
        case 'ProductList':
            const { ProductList } = await import('./components/ProductList');
            return ProductList;
        default:
            throw new Error(`未知组件: ${componentName}`);
    }
}
```

### 2. 模块增强

```typescript
// 扩展现有模块
declare module './user' {
    interface User {
        getFullName(): string;
    }
}

// 全局模块增强
declare global {
    interface Window {
        myApp: {
            version: string;
            config: any;
        };
    }
}
```

### 3. 命名空间模块

```typescript
// legacy-module.ts - 使用命名空间的旧式模块
export namespace Geometry {
    export interface Point {
        x: number;
        y: number;
    }
    
    export class Circle {
        constructor(
            public center: Point,
            public radius: number
        ) {}
        
        getArea(): number {
            return Math.PI * this.radius * this.radius;
        }
    }
    
    export namespace Utils {
        export function distance(p1: Point, p2: Point): number {
            return Math.sqrt(
                Math.pow(p2.x - p1.x, 2) + Math.pow(p2.y - p1.y, 2)
            );
        }
    }
}
```

## 📝 实际应用示例

### 1. 用户管理模块

```typescript
// models/user.ts
export interface IUser {
    id: number;
    username: string;
    email: string;
    createdAt: Date;
}

export class User implements IUser {
    constructor(
        public id: number,
        public username: string,
        public email: string,
        public createdAt: Date = new Date()
    ) {}
    
    getDisplayName(): string {
        return `${this.username} (${this.email})`;
    }
}

export type UserCreateInput = Omit<IUser, 'id' | 'createdAt'>;
export type UserUpdateInput = Partial<Pick<IUser, 'username' | 'email'>>;
```

```typescript
// services/userService.ts
import { User, IUser, UserCreateInput, UserUpdateInput } from '../models/user';

export class UserService {
    private users: User[] = [];
    private nextId = 1;
    
    createUser(input: UserCreateInput): User {
        const user = new User(
            this.nextId++,
            input.username,
            input.email
        );
        this.users.push(user);
        return user;
    }
    
    getUserById(id: number): User | undefined {
        return this.users.find(user => user.id === id);
    }
    
    updateUser(id: number, input: UserUpdateInput): User | null {
        const user = this.getUserById(id);
        if (!user) return null;
        
        if (input.username) user.username = input.username;
        if (input.email) user.email = input.email;
        
        return user;
    }
    
    deleteUser(id: number): boolean {
        const index = this.users.findIndex(user => user.id === id);
        if (index === -1) return false;
        
        this.users.splice(index, 1);
        return true;
    }
    
    getAllUsers(): User[] {
        return [...this.users];
    }
}

// 单例模式
export const userService = new UserService();
```

```typescript
// index.ts - 应用入口
import { userService } from './services/userService';
import { UserCreateInput } from './models/user';

// 创建用户
const newUser: UserCreateInput = {
    username: "zhangsan",
    email: "<EMAIL>"
};

const user = userService.createUser(newUser);
console.log(`创建用户: ${user.getDisplayName()}`);

// 获取所有用户
const allUsers = userService.getAllUsers();
console.log(`总用户数: ${allUsers.length}`);
```

### 2. 配置模块

```typescript
// config/database.ts
export interface DatabaseConfig {
    host: string;
    port: number;
    username: string;
    password: string;
    database: string;
}

export const databaseConfig: DatabaseConfig = {
    host: process.env.DB_HOST || 'localhost',
    port: parseInt(process.env.DB_PORT || '3306'),
    username: process.env.DB_USERNAME || 'root',
    password: process.env.DB_PASSWORD || '',
    database: process.env.DB_NAME || 'myapp'
};
```

```typescript
// config/index.ts
export { databaseConfig } from './database';
export { apiConfig } from './api';
export { appConfig } from './app';

// 统一配置对象
export const config = {
    database: databaseConfig,
    api: apiConfig,
    app: appConfig
};
```

## 🎯 本章小结

模块系统是现代TypeScript开发的基础：
- 使用ES6模块语法进行导入导出
- 合理组织模块结构提高代码可维护性
- 利用索引文件简化导入路径
- 动态导入支持代码分割和懒加载
- 路径映射改善开发体验

良好的模块组织是大型项目成功的关键因素。

---

**下一章：[命名空间](../10-namespaces/README.md)**
