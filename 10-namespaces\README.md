# 第10章：命名空间

## 🎯 学习目标
- 理解命名空间的概念和用途
- 掌握命名空间的定义和使用
- 学会命名空间的嵌套和合并
- 了解命名空间与模块的区别

## 📖 命名空间基础

命名空间（Namespace）是TypeScript早期用于组织代码的方式，主要用于避免全局命名冲突。

> **注意**: 在现代TypeScript开发中，推荐使用ES6模块而不是命名空间。命名空间主要用于：
> - 维护旧代码
> - 全局库的类型定义
> - 特定的组织需求

### 1. 基本命名空间

```typescript
// 定义命名空间
namespace Geometry {
    // 导出接口
    export interface Point {
        x: number;
        y: number;
    }
    
    // 导出类
    export class Circle {
        constructor(
            public center: Point,
            public radius: number
        ) {}
        
        getArea(): number {
            return Math.PI * this.radius * this.radius;
        }
        
        getCircumference(): number {
            return 2 * Math.PI * this.radius;
        }
    }
    
    // 导出函数
    export function distance(p1: Point, p2: Point): number {
        return Math.sqrt(
            Math.pow(p2.x - p1.x, 2) + Math.pow(p2.y - p1.y, 2)
        );
    }
    
    // 内部函数（不导出）
    function validatePoint(point: Point): boolean {
        return typeof point.x === 'number' && typeof point.y === 'number';
    }
    
    // 导出常量
    export const PI = Math.PI;
}

// 使用命名空间
const center: Geometry.Point = { x: 0, y: 0 };
const circle = new Geometry.Circle(center, 5);

console.log(`圆的面积: ${circle.getArea()}`);
console.log(`圆周长: ${circle.getCircumference()}`);

const point1: Geometry.Point = { x: 0, y: 0 };
const point2: Geometry.Point = { x: 3, y: 4 };
console.log(`两点距离: ${Geometry.distance(point1, point2)}`);
```

### 2. 嵌套命名空间

```typescript
namespace Company {
    export namespace HR {
        export interface Employee {
            id: number;
            name: string;
            department: string;
            salary: number;
        }
        
        export class EmployeeManager {
            private employees: Employee[] = [];
            
            addEmployee(employee: Employee): void {
                this.employees.push(employee);
            }
            
            getEmployee(id: number): Employee | undefined {
                return this.employees.find(emp => emp.id === id);
            }
            
            getEmployeesByDepartment(department: string): Employee[] {
                return this.employees.filter(emp => emp.department === department);
            }
        }
    }
    
    export namespace Finance {
        export interface Budget {
            department: string;
            amount: number;
            year: number;
        }
        
        export class BudgetManager {
            private budgets: Budget[] = [];
            
            setBudget(budget: Budget): void {
                const existing = this.budgets.findIndex(
                    b => b.department === budget.department && b.year === budget.year
                );
                
                if (existing >= 0) {
                    this.budgets[existing] = budget;
                } else {
                    this.budgets.push(budget);
                }
            }
            
            getBudget(department: string, year: number): Budget | undefined {
                return this.budgets.find(
                    b => b.department === department && b.year === year
                );
            }
        }
    }
    
    // 跨命名空间的工具函数
    export namespace Utils {
        export function calculateTotalSalary(employees: HR.Employee[]): number {
            return employees.reduce((total, emp) => total + emp.salary, 0);
        }
        
        export function generateEmployeeReport(
            employees: HR.Employee[],
            budget: Finance.Budget
        ): string {
            const totalSalary = calculateTotalSalary(employees);
            const remaining = budget.amount - totalSalary;
            
            return `部门: ${budget.department}
预算: ¥${budget.amount}
实际支出: ¥${totalSalary}
剩余预算: ¥${remaining}`;
        }
    }
}

// 使用嵌套命名空间
const hrManager = new Company.HR.EmployeeManager();
const financeManager = new Company.Finance.BudgetManager();

// 添加员工
const employee: Company.HR.Employee = {
    id: 1,
    name: "张三",
    department: "技术部",
    salary: 8000
};

hrManager.addEmployee(employee);

// 设置预算
const budget: Company.Finance.Budget = {
    department: "技术部",
    amount: 50000,
    year: 2024
};

financeManager.setBudget(budget);

// 生成报告
const techEmployees = hrManager.getEmployeesByDepartment("技术部");
const techBudget = financeManager.getBudget("技术部", 2024);

if (techBudget) {
    const report = Company.Utils.generateEmployeeReport(techEmployees, techBudget);
    console.log(report);
}
```

### 3. 命名空间别名

```typescript
// 为长命名空间创建别名
namespace VeryLongCompanyNamespace {
    export namespace HumanResourcesDepartment {
        export namespace EmployeeManagementSystem {
            export class Employee {
                constructor(public name: string) {}
            }
        }
    }
}

// 创建别名
import EMS = VeryLongCompanyNamespace.HumanResourcesDepartment.EmployeeManagementSystem;

// 使用别名
const employee = new EMS.Employee("李四");

// 也可以为类型创建别名
type Employee = VeryLongCompanyNamespace.HumanResourcesDepartment.EmployeeManagementSystem.Employee;
```

## 🔄 命名空间合并

```typescript
// 第一个文件或代码块
namespace Animals {
    export class Dog {
        name: string;
        constructor(name: string) {
            this.name = name;
        }
        
        bark(): void {
            console.log(`${this.name} 汪汪叫`);
        }
    }
}

// 第二个文件或代码块 - 扩展同一个命名空间
namespace Animals {
    export class Cat {
        name: string;
        constructor(name: string) {
            this.name = name;
        }
        
        meow(): void {
            console.log(`${this.name} 喵喵叫`);
        }
    }
    
    // 添加工具函数
    export function createPet(type: "dog" | "cat", name: string): Dog | Cat {
        switch (type) {
            case "dog":
                return new Dog(name);
            case "cat":
                return new Cat(name);
        }
    }
}

// 使用合并后的命名空间
const dog = new Animals.Dog("旺财");
const cat = new Animals.Cat("咪咪");
const pet = Animals.createPet("dog", "小黄");

dog.bark();
cat.meow();
```

## 🌐 全局命名空间

```typescript
// 声明全局命名空间
declare global {
    namespace MyGlobalLibrary {
        interface Config {
            apiUrl: string;
            timeout: number;
        }
        
        function initialize(config: Config): void;
        function getVersion(): string;
    }
}

// 实现全局命名空间
(global as any).MyGlobalLibrary = {
    initialize(config: MyGlobalLibrary.Config): void {
        console.log(`初始化库，API URL: ${config.apiUrl}`);
    },
    
    getVersion(): string {
        return "1.0.0";
    }
};

// 使用全局命名空间
MyGlobalLibrary.initialize({
    apiUrl: "https://api.example.com",
    timeout: 5000
});

console.log(`库版本: ${MyGlobalLibrary.getVersion()}`);
```

## 🔧 命名空间与模块的结合

```typescript
// math-namespace.ts - 导出命名空间
export namespace MathOperations {
    export interface Operation {
        name: string;
        execute(a: number, b: number): number;
    }
    
    export class Addition implements Operation {
        name = "加法";
        
        execute(a: number, b: number): number {
            return a + b;
        }
    }
    
    export class Multiplication implements Operation {
        name = "乘法";
        
        execute(a: number, b: number): number {
            return a * b;
        }
    }
    
    export namespace Advanced {
        export function power(base: number, exponent: number): number {
            return Math.pow(base, exponent);
        }
        
        export function factorial(n: number): number {
            if (n <= 1) return 1;
            return n * factorial(n - 1);
        }
    }
}

// 默认导出计算器类
export default class Calculator {
    private operations: MathOperations.Operation[] = [];
    
    addOperation(operation: MathOperations.Operation): void {
        this.operations.push(operation);
    }
    
    calculate(operationName: string, a: number, b: number): number | null {
        const operation = this.operations.find(op => op.name === operationName);
        return operation ? operation.execute(a, b) : null;
    }
}
```

```typescript
// main.ts - 使用命名空间和模块
import Calculator, { MathOperations } from './math-namespace';

const calculator = new Calculator();

// 添加操作
calculator.addOperation(new MathOperations.Addition());
calculator.addOperation(new MathOperations.Multiplication());

// 执行计算
console.log(calculator.calculate("加法", 5, 3)); // 8
console.log(calculator.calculate("乘法", 4, 6)); // 24

// 使用高级数学函数
console.log(MathOperations.Advanced.power(2, 3)); // 8
console.log(MathOperations.Advanced.factorial(5)); // 120
```

## 📝 实际应用场景

### 1. 游戏开发

```typescript
namespace Game {
    export namespace Entities {
        export abstract class GameObject {
            constructor(
                public x: number,
                public y: number,
                public width: number,
                public height: number
            ) {}
            
            abstract update(deltaTime: number): void;
            abstract render(context: CanvasRenderingContext2D): void;
        }
        
        export class Player extends GameObject {
            constructor(x: number, y: number) {
                super(x, y, 32, 32);
            }
            
            update(deltaTime: number): void {
                // 更新玩家逻辑
            }
            
            render(context: CanvasRenderingContext2D): void {
                context.fillStyle = "blue";
                context.fillRect(this.x, this.y, this.width, this.height);
            }
        }
        
        export class Enemy extends GameObject {
            constructor(x: number, y: number) {
                super(x, y, 24, 24);
            }
            
            update(deltaTime: number): void {
                // 更新敌人逻辑
            }
            
            render(context: CanvasRenderingContext2D): void {
                context.fillStyle = "red";
                context.fillRect(this.x, this.y, this.width, this.height);
            }
        }
    }
    
    export namespace Utils {
        export function checkCollision(
            obj1: Entities.GameObject,
            obj2: Entities.GameObject
        ): boolean {
            return obj1.x < obj2.x + obj2.width &&
                   obj1.x + obj1.width > obj2.x &&
                   obj1.y < obj2.y + obj2.height &&
                   obj1.y + obj1.height > obj2.y;
        }
        
        export function distance(
            obj1: Entities.GameObject,
            obj2: Entities.GameObject
        ): number {
            const dx = obj1.x - obj2.x;
            const dy = obj1.y - obj2.y;
            return Math.sqrt(dx * dx + dy * dy);
        }
    }
    
    export class GameEngine {
        private entities: Entities.GameObject[] = [];
        
        addEntity(entity: Entities.GameObject): void {
            this.entities.push(entity);
        }
        
        update(deltaTime: number): void {
            this.entities.forEach(entity => entity.update(deltaTime));
        }
        
        render(context: CanvasRenderingContext2D): void {
            context.clearRect(0, 0, 800, 600);
            this.entities.forEach(entity => entity.render(context));
        }
    }
}
```

### 2. API客户端库

```typescript
namespace ApiClient {
    export namespace Types {
        export interface ApiResponse<T> {
            success: boolean;
            data: T;
            message: string;
        }
        
        export interface User {
            id: number;
            name: string;
            email: string;
        }
        
        export interface Product {
            id: number;
            name: string;
            price: number;
        }
    }
    
    export namespace Endpoints {
        export class UserEndpoint {
            constructor(private baseUrl: string) {}
            
            async getUser(id: number): Promise<Types.ApiResponse<Types.User>> {
                // 实现API调用
                return {
                    success: true,
                    data: { id, name: "测试用户", email: "<EMAIL>" },
                    message: "成功"
                };
            }
        }
        
        export class ProductEndpoint {
            constructor(private baseUrl: string) {}
            
            async getProduct(id: number): Promise<Types.ApiResponse<Types.Product>> {
                // 实现API调用
                return {
                    success: true,
                    data: { id, name: "测试产品", price: 99.99 },
                    message: "成功"
                };
            }
        }
    }
    
    export class Client {
        public users: Endpoints.UserEndpoint;
        public products: Endpoints.ProductEndpoint;
        
        constructor(baseUrl: string) {
            this.users = new Endpoints.UserEndpoint(baseUrl);
            this.products = new Endpoints.ProductEndpoint(baseUrl);
        }
    }
}
```

## 🎯 本章小结

命名空间提供了另一种组织代码的方式：
- 避免全局命名冲突
- 支持嵌套和合并
- 可以与模块系统结合使用
- 主要用于维护旧代码和类型定义

在现代TypeScript开发中，推荐优先使用ES6模块，只在特定场景下使用命名空间。

---

**下一章：[装饰器](../11-decorators/README.md)**
