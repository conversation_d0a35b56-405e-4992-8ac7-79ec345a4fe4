# 第11章：装饰器

## 🎯 学习目标
- 理解装饰器的概念和用途
- 掌握类装饰器、方法装饰器、属性装饰器
- 学会参数装饰器和访问器装饰器
- 了解装饰器的实际应用场景

## 📖 装饰器基础

装饰器（Decorator）是一种特殊的声明，可以附加到类、方法、属性或参数上，用于修改类的行为。

> **注意**: 装饰器是实验性功能，需要在`tsconfig.json`中启用：
> ```json
> {
>   "compilerOptions": {
>     "experimentalDecorators": true,
>     "emitDecoratorMetadata": true
>   }
> }
> ```

### 1. 类装饰器

```typescript
// 简单的类装饰器
function sealed(constructor: Function) {
    Object.seal(constructor);
    Object.seal(constructor.prototype);
}

@sealed
class Greeter {
    greeting: string;
    
    constructor(message: string) {
        this.greeting = message;
    }
    
    greet() {
        return `Hello, ${this.greeting}`;
    }
}

// 带参数的类装饰器
function classInfo(name: string, version: string) {
    return function <T extends { new(...args: any[]): {} }>(constructor: T) {
        return class extends constructor {
            className = name;
            version = version;
            createdAt = new Date();
            
            getInfo() {
                return `${name} v${version} - 创建于 ${this.createdAt}`;
            }
        };
    };
}

@classInfo("UserService", "1.0.0")
class UserService {
    constructor(public name: string) {}
    
    getUser() {
        return `获取用户: ${this.name}`;
    }
}

const service = new UserService("张三") as any;
console.log(service.getInfo()); // UserService v1.0.0 - 创建于 ...
```

### 2. 方法装饰器

```typescript
// 日志装饰器
function log(target: any, propertyName: string, descriptor: PropertyDescriptor) {
    const method = descriptor.value;
    
    descriptor.value = function (...args: any[]) {
        console.log(`调用方法 ${propertyName}，参数:`, args);
        const result = method.apply(this, args);
        console.log(`方法 ${propertyName} 返回:`, result);
        return result;
    };
}

// 性能监控装饰器
function measure(target: any, propertyName: string, descriptor: PropertyDescriptor) {
    const method = descriptor.value;
    
    descriptor.value = function (...args: any[]) {
        const start = performance.now();
        const result = method.apply(this, args);
        const end = performance.now();
        console.log(`方法 ${propertyName} 执行时间: ${end - start}ms`);
        return result;
    };
}

// 缓存装饰器
function cache(target: any, propertyName: string, descriptor: PropertyDescriptor) {
    const method = descriptor.value;
    const cacheMap = new Map();
    
    descriptor.value = function (...args: any[]) {
        const key = JSON.stringify(args);
        
        if (cacheMap.has(key)) {
            console.log(`缓存命中: ${propertyName}`);
            return cacheMap.get(key);
        }
        
        const result = method.apply(this, args);
        cacheMap.set(key, result);
        console.log(`缓存存储: ${propertyName}`);
        return result;
    };
}

class Calculator {
    @log
    @measure
    add(a: number, b: number): number {
        return a + b;
    }
    
    @cache
    fibonacci(n: number): number {
        if (n <= 1) return n;
        return this.fibonacci(n - 1) + this.fibonacci(n - 2);
    }
}

const calc = new Calculator();
calc.add(5, 3);
calc.fibonacci(10);
calc.fibonacci(10); // 第二次调用会使用缓存
```

### 3. 属性装饰器

```typescript
// 只读装饰器
function readonly(target: any, propertyName: string) {
    Object.defineProperty(target, propertyName, {
        writable: false,
        configurable: false
    });
}

// 格式化装饰器
function format(formatFn: (value: any) => any) {
    return function (target: any, propertyName: string) {
        let value: any;
        
        Object.defineProperty(target, propertyName, {
            get() {
                return value;
            },
            set(newValue: any) {
                value = formatFn(newValue);
            },
            enumerable: true,
            configurable: true
        });
    };
}

// 验证装饰器
function validate(validationFn: (value: any) => boolean, errorMessage: string) {
    return function (target: any, propertyName: string) {
        let value: any;
        
        Object.defineProperty(target, propertyName, {
            get() {
                return value;
            },
            set(newValue: any) {
                if (!validationFn(newValue)) {
                    throw new Error(`${propertyName}: ${errorMessage}`);
                }
                value = newValue;
            },
            enumerable: true,
            configurable: true
        });
    };
}

class User {
    @readonly
    id: number = 1;
    
    @format((value: string) => value.trim().toLowerCase())
    email: string = "";
    
    @validate((value: string) => value.length >= 2, "姓名至少2个字符")
    @format((value: string) => value.trim())
    name: string = "";
    
    @validate((value: number) => value >= 0 && value <= 150, "年龄必须在0-150之间")
    age: number = 0;
}

const user = new User();
user.email = "  <EMAIL>  "; // 自动格式化为 "<EMAIL>"
user.name = "张三";
user.age = 25;

console.log(user.email); // "<EMAIL>"

// user.id = 2; // 错误：只读属性
// user.age = 200; // 错误：验证失败
```

### 4. 参数装饰器

```typescript
// 参数验证装饰器
function required(target: any, propertyName: string, parameterIndex: number) {
    const existingRequiredParameters: number[] = 
        Reflect.getOwnMetadata('required', target, propertyName) || [];
    existingRequiredParameters.push(parameterIndex);
    Reflect.defineMetadata('required', existingRequiredParameters, target, propertyName);
}

function validate(target: any, propertyName: string, descriptor: PropertyDescriptor) {
    const method = descriptor.value;
    
    descriptor.value = function (...args: any[]) {
        const requiredParameters: number[] = 
            Reflect.getOwnMetadata('required', target, propertyName) || [];
        
        for (const parameterIndex of requiredParameters) {
            if (args[parameterIndex] === undefined || args[parameterIndex] === null) {
                throw new Error(`参数 ${parameterIndex} 是必需的`);
            }
        }
        
        return method.apply(this, args);
    };
}

class UserController {
    @validate
    createUser(@required name: string, @required email: string, age?: number) {
        console.log(`创建用户: ${name}, ${email}, ${age || '未知年龄'}`);
    }
}

const controller = new UserController();
controller.createUser("张三", "<EMAIL>", 25); // 正常
// controller.createUser("张三", null); // 错误：参数1是必需的
```

## 🔧 装饰器工厂

```typescript
// 装饰器工厂 - 返回装饰器的函数
function Component(options: { selector: string; template: string }) {
    return function <T extends { new(...args: any[]): {} }>(constructor: T) {
        return class extends constructor {
            selector = options.selector;
            template = options.template;
            
            render() {
                console.log(`渲染组件 ${this.selector}: ${this.template}`);
            }
        };
    };
}

function Injectable(token?: string) {
    return function <T extends { new(...args: any[]): {} }>(constructor: T) {
        // 注册到依赖注入容器
        console.log(`注册服务: ${token || constructor.name}`);
        return constructor;
    };
}

function Route(path: string, method: 'GET' | 'POST' | 'PUT' | 'DELETE' = 'GET') {
    return function (target: any, propertyName: string, descriptor: PropertyDescriptor) {
        // 注册路由
        console.log(`注册路由: ${method} ${path} -> ${propertyName}`);
    };
}

@Component({
    selector: 'app-user',
    template: '<div>用户组件</div>'
})
class UserComponent {
    constructor(public name: string) {}
}

@Injectable('userService')
class UserService {
    getUsers() {
        return ['张三', '李四', '王五'];
    }
}

class UserController {
    @Route('/users', 'GET')
    getUsers() {
        return "获取用户列表";
    }
    
    @Route('/users/:id', 'GET')
    getUser() {
        return "获取单个用户";
    }
    
    @Route('/users', 'POST')
    createUser() {
        return "创建用户";
    }
}
```

## 🌟 实际应用示例

### 1. 简单的依赖注入系统

```typescript
// 依赖注入容器
class Container {
    private static services = new Map<string, any>();
    
    static register<T>(token: string, service: T): void {
        this.services.set(token, service);
    }
    
    static get<T>(token: string): T {
        const service = this.services.get(token);
        if (!service) {
            throw new Error(`服务 ${token} 未注册`);
        }
        return service;
    }
}

// 注入装饰器
function Inject(token: string) {
    return function (target: any, propertyName: string) {
        Object.defineProperty(target, propertyName, {
            get() {
                return Container.get(token);
            },
            enumerable: true,
            configurable: true
        });
    };
}

// 服务装饰器
function Service(token: string) {
    return function <T extends { new(...args: any[]): {} }>(constructor: T) {
        Container.register(token, new constructor());
        return constructor;
    };
}

@Service('logger')
class Logger {
    log(message: string): void {
        console.log(`[LOG] ${message}`);
    }
}

@Service('database')
class Database {
    connect(): void {
        console.log("连接数据库");
    }
    
    query(sql: string): any[] {
        console.log(`执行查询: ${sql}`);
        return [];
    }
}

class UserService {
    @Inject('logger')
    private logger!: Logger;
    
    @Inject('database')
    private db!: Database;
    
    createUser(name: string): void {
        this.logger.log(`创建用户: ${name}`);
        this.db.connect();
        this.db.query(`INSERT INTO users (name) VALUES ('${name}')`);
    }
}

const userService = new UserService();
userService.createUser("张三");
```

### 2. API路由装饰器

```typescript
// 路由元数据
interface RouteMetadata {
    path: string;
    method: string;
    handler: string;
}

// 控制器元数据
const routeMetadata = new Map<Function, RouteMetadata[]>();

// HTTP方法装饰器
function Get(path: string) {
    return createRouteDecorator('GET', path);
}

function Post(path: string) {
    return createRouteDecorator('POST', path);
}

function Put(path: string) {
    return createRouteDecorator('PUT', path);
}

function Delete(path: string) {
    return createRouteDecorator('DELETE', path);
}

function createRouteDecorator(method: string, path: string) {
    return function (target: any, propertyName: string, descriptor: PropertyDescriptor) {
        const constructor = target.constructor;
        const routes = routeMetadata.get(constructor) || [];
        
        routes.push({
            path,
            method,
            handler: propertyName
        });
        
        routeMetadata.set(constructor, routes);
    };
}

// 控制器装饰器
function Controller(basePath: string = '') {
    return function <T extends { new(...args: any[]): {} }>(constructor: T) {
        const routes = routeMetadata.get(constructor) || [];
        
        console.log(`注册控制器: ${constructor.name}`);
        routes.forEach(route => {
            const fullPath = basePath + route.path;
            console.log(`  ${route.method} ${fullPath} -> ${route.handler}`);
        });
        
        return constructor;
    };
}

@Controller('/api/users')
class UserController {
    @Get('/')
    getAllUsers() {
        return "获取所有用户";
    }
    
    @Get('/:id')
    getUser() {
        return "获取单个用户";
    }
    
    @Post('/')
    createUser() {
        return "创建用户";
    }
    
    @Put('/:id')
    updateUser() {
        return "更新用户";
    }
    
    @Delete('/:id')
    deleteUser() {
        return "删除用户";
    }
}
```

## 🎯 本章小结

装饰器是TypeScript中强大的元编程工具：
- 类装饰器用于修改类的行为
- 方法装饰器可以拦截和修改方法调用
- 属性装饰器控制属性的访问和设置
- 参数装饰器用于参数验证和处理
- 装饰器工厂提供配置化的装饰器

装饰器在框架开发、依赖注入、AOP编程等场景中非常有用，但要注意它仍是实验性功能。

---

**下一章：[高级类型](../12-advanced-types/README.md)**
