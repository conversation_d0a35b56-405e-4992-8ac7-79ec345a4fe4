# 第12章：高级类型

## 🎯 学习目标
- 掌握联合类型和交叉类型的高级用法
- 理解条件类型和映射类型
- 学会使用模板字面量类型
- 了解类型守卫和类型断言

## 📖 联合类型和交叉类型

### 1. 联合类型的高级用法

```typescript
// 字面量联合类型
type Theme = "light" | "dark" | "auto";
type Size = "small" | "medium" | "large";
type Status = "pending" | "approved" | "rejected";

// 函数重载的替代方案
type EventHandler = 
    | ((event: MouseEvent) => void)
    | ((event: KeyboardEvent) => void)
    | ((event: TouchEvent) => void);

// 区分联合类型
interface LoadingState {
    status: "loading";
}

interface SuccessState {
    status: "success";
    data: any;
}

interface ErrorState {
    status: "error";
    error: string;
}

type AsyncState = LoadingState | SuccessState | ErrorState;

function handleState(state: AsyncState) {
    switch (state.status) {
        case "loading":
            console.log("加载中...");
            break;
        case "success":
            console.log("数据:", state.data); // TypeScript知道这里有data属性
            break;
        case "error":
            console.log("错误:", state.error); // TypeScript知道这里有error属性
            break;
    }
}
```

### 2. 交叉类型的实际应用

```typescript
// 混入模式
interface Timestamped {
    createdAt: Date;
    updatedAt: Date;
}

interface Identifiable {
    id: string;
}

interface Auditable {
    createdBy: string;
    updatedBy: string;
}

// 组合多个接口
type User = {
    name: string;
    email: string;
} & Timestamped & Identifiable & Auditable;

// 函数参数的交叉类型
function createUser(
    userData: { name: string; email: string },
    metadata: Timestamped & Auditable
): User {
    return {
        ...userData,
        ...metadata,
        id: Math.random().toString(36)
    };
}

// 条件交叉类型
type ApiResponse<T> = {
    success: boolean;
    message: string;
} & (
    | { success: true; data: T }
    | { success: false; error: string }
);
```

## 🔍 条件类型

```typescript
// 基本条件类型
type IsArray<T> = T extends any[] ? true : false;

type Test1 = IsArray<string[]>; // true
type Test2 = IsArray<number>;   // false

// 提取类型信息
type GetArrayElementType<T> = T extends (infer U)[] ? U : never;

type StringArrayElement = GetArrayElementType<string[]>; // string
type NumberArrayElement = GetArrayElementType<number[]>; // number

// 函数返回类型提取
type ReturnType<T> = T extends (...args: any[]) => infer R ? R : never;

function getString(): string { return "hello"; }
function getNumber(): number { return 42; }

type StringReturn = ReturnType<typeof getString>; // string
type NumberReturn = ReturnType<typeof getNumber>; // number

// 复杂条件类型
type NonNullable<T> = T extends null | undefined ? never : T;

type Test3 = NonNullable<string | null>;      // string
type Test4 = NonNullable<number | undefined>; // number

// 分布式条件类型
type ToArray<T> = T extends any ? T[] : never;

type StringOrNumberArray = ToArray<string | number>; // string[] | number[]
```

## 🗺️ 映射类型

```typescript
// 基本映射类型
type Readonly<T> = {
    readonly [P in keyof T]: T[P];
};

type Partial<T> = {
    [P in keyof T]?: T[P];
};

type Required<T> = {
    [P in keyof T]-?: T[P];
};

// 自定义映射类型
type Stringify<T> = {
    [P in keyof T]: string;
};

type Nullify<T> = {
    [P in keyof T]: T[P] | null;
};

interface User {
    id: number;
    name: string;
    email: string;
    age: number;
}

type ReadonlyUser = Readonly<User>;
type PartialUser = Partial<User>;
type StringifiedUser = Stringify<User>;
type NullableUser = Nullify<User>;

// 键重映射
type Getters<T> = {
    [P in keyof T as `get${Capitalize<string & P>}`]: () => T[P];
};

type UserGetters = Getters<User>;
// {
//     getId: () => number;
//     getName: () => string;
//     getEmail: () => string;
//     getAge: () => number;
// }

// 条件映射
type NonFunctionPropertyNames<T> = {
    [K in keyof T]: T[K] extends Function ? never : K;
}[keyof T];

type NonFunctionProperties<T> = Pick<T, NonFunctionPropertyNames<T>>;

class Example {
    name: string = "";
    age: number = 0;
    getName(): string { return this.name; }
    getAge(): number { return this.age; }
}

type ExampleData = NonFunctionProperties<Example>; // { name: string; age: number; }
```

## 📝 模板字面量类型

```typescript
// 基本模板字面量类型
type Greeting = `Hello, ${string}!`;

let greeting1: Greeting = "Hello, World!";     // ✓
let greeting2: Greeting = "Hello, TypeScript!"; // ✓
// let greeting3: Greeting = "Hi, World!";      // ✗

// 结合联合类型
type Color = "red" | "green" | "blue";
type Size = "small" | "medium" | "large";

type ColoredSize = `${Color}-${Size}`;
// "red-small" | "red-medium" | "red-large" | 
// "green-small" | "green-medium" | "green-large" | 
// "blue-small" | "blue-medium" | "blue-large"

// 事件名称生成
type EventName<T extends string> = `on${Capitalize<T>}`;

type ClickEvent = EventName<"click">;     // "onClick"
type ChangeEvent = EventName<"change">;   // "onChange"

// API路径生成
type HttpMethod = "GET" | "POST" | "PUT" | "DELETE";
type ApiPath = "/users" | "/products" | "/orders";

type ApiEndpoint = `${HttpMethod} ${ApiPath}`;
// "GET /users" | "POST /users" | "PUT /users" | "DELETE /users" | ...

// 深度路径类型
type DeepPath<T, K extends keyof T = keyof T> = K extends string
    ? T[K] extends Record<string, any>
        ? `${K}` | `${K}.${DeepPath<T[K]>}`
        : `${K}`
    : never;

interface Config {
    database: {
        host: string;
        port: number;
        credentials: {
            username: string;
            password: string;
        };
    };
    api: {
        baseUrl: string;
        timeout: number;
    };
}

type ConfigPath = DeepPath<Config>;
// "database" | "database.host" | "database.port" |
// "database.credentials" | "database.credentials.username" |
// "database.credentials.password" | "api" | "api.baseUrl" | "api.timeout"
```

## 🛡️ 类型守卫

```typescript
// 基本类型守卫
function isString(value: any): value is string {
    return typeof value === "string";
}

function isNumber(value: any): value is number {
    return typeof value === "number";
}

function processValue(value: string | number) {
    if (isString(value)) {
        console.log(value.toUpperCase()); // TypeScript知道这里是string
    } else if (isNumber(value)) {
        console.log(value.toFixed(2));    // TypeScript知道这里是number
    }
}

// 对象类型守卫
interface User {
    type: "user";
    name: string;
    email: string;
}

interface Admin {
    type: "admin";
    name: string;
    permissions: string[];
}

type Person = User | Admin;

function isUser(person: Person): person is User {
    return person.type === "user";
}

function isAdmin(person: Person): person is Admin {
    return person.type === "admin";
}

function handlePerson(person: Person) {
    if (isUser(person)) {
        console.log(`用户: ${person.name}, 邮箱: ${person.email}`);
    } else if (isAdmin(person)) {
        console.log(`管理员: ${person.name}, 权限: ${person.permissions.join(", ")}`);
    }
}

// 数组类型守卫
function isStringArray(value: any): value is string[] {
    return Array.isArray(value) && value.every(item => typeof item === "string");
}

function isNumberArray(value: any): value is number[] {
    return Array.isArray(value) && value.every(item => typeof item === "number");
}

// 类实例守卫
class Dog {
    bark() { console.log("汪汪"); }
}

class Cat {
    meow() { console.log("喵喵"); }
}

function isDog(animal: Dog | Cat): animal is Dog {
    return animal instanceof Dog;
}

function makeSound(animal: Dog | Cat) {
    if (isDog(animal)) {
        animal.bark();
    } else {
        animal.meow();
    }
}
```

## 🔧 实用工具类型

```typescript
// 内置工具类型的高级用法
interface User {
    id: number;
    name: string;
    email: string;
    password: string;
    createdAt: Date;
    updatedAt: Date;
}

// Pick - 选择特定属性
type UserPublic = Pick<User, "id" | "name" | "email">;

// Omit - 排除特定属性
type UserCreate = Omit<User, "id" | "createdAt" | "updatedAt">;

// Record - 创建记录类型
type UserRoles = Record<number, "admin" | "user" | "guest">;

// Parameters - 提取函数参数类型
function createUser(name: string, email: string, age: number): User {
    return {} as User;
}

type CreateUserParams = Parameters<typeof createUser>; // [string, string, number]

// ConstructorParameters - 提取构造函数参数类型
class UserService {
    constructor(apiUrl: string, timeout: number) {}
}

type UserServiceParams = ConstructorParameters<typeof UserService>; // [string, number]

// 自定义工具类型
type DeepPartial<T> = {
    [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P];
};

type DeepRequired<T> = {
    [P in keyof T]-?: T[P] extends object ? DeepRequired<T[P]> : T[P];
};

type KeysOfType<T, U> = {
    [K in keyof T]: T[K] extends U ? K : never;
}[keyof T];

type StringKeys = KeysOfType<User, string>; // "name" | "email" | "password"
type NumberKeys = KeysOfType<User, number>; // "id"
```

## 🎯 实际应用示例

```typescript
// 状态管理类型系统
type ActionType = "SET_USER" | "UPDATE_USER" | "DELETE_USER" | "SET_LOADING";

interface SetUserAction {
    type: "SET_USER";
    payload: User;
}

interface UpdateUserAction {
    type: "UPDATE_USER";
    payload: Partial<User> & { id: number };
}

interface DeleteUserAction {
    type: "DELETE_USER";
    payload: { id: number };
}

interface SetLoadingAction {
    type: "SET_LOADING";
    payload: boolean;
}

type Action = SetUserAction | UpdateUserAction | DeleteUserAction | SetLoadingAction;

// 根据action类型提取payload类型
type ActionPayload<T extends ActionType> = Extract<Action, { type: T }>["payload"];

type SetUserPayload = ActionPayload<"SET_USER">;       // User
type UpdateUserPayload = ActionPayload<"UPDATE_USER">; // Partial<User> & { id: number }

// 表单验证类型系统
type ValidationRule<T> = {
    required?: boolean;
    minLength?: number;
    maxLength?: number;
    pattern?: RegExp;
    custom?: (value: T) => string | null;
};

type FormValidation<T> = {
    [K in keyof T]?: ValidationRule<T[K]>;
};

type UserFormValidation = FormValidation<UserCreate>;

const userValidation: UserFormValidation = {
    name: {
        required: true,
        minLength: 2,
        maxLength: 50
    },
    email: {
        required: true,
        pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    },
    password: {
        required: true,
        minLength: 8,
        custom: (value) => {
            if (!/(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/.test(value)) {
                return "密码必须包含大小写字母和数字";
            }
            return null;
        }
    }
};

// API响应类型系统
type ApiMethod = "GET" | "POST" | "PUT" | "DELETE";

type ApiEndpoints = {
    "GET /users": { response: User[] };
    "GET /users/:id": { response: User };
    "POST /users": { body: UserCreate; response: User };
    "PUT /users/:id": { body: Partial<UserCreate>; response: User };
    "DELETE /users/:id": { response: { success: boolean } };
};

type ExtractResponse<T extends keyof ApiEndpoints> = ApiEndpoints[T]["response"];
type ExtractBody<T extends keyof ApiEndpoints> = ApiEndpoints[T] extends { body: infer B } ? B : never;

type GetUsersResponse = ExtractResponse<"GET /users">;     // User[]
type CreateUserBody = ExtractBody<"POST /users">;         // UserCreate
```

## 🎯 本章小结

高级类型是TypeScript的强大特性：
- 联合类型和交叉类型提供灵活的类型组合
- 条件类型支持类型级别的逻辑判断
- 映射类型实现类型转换和生成
- 模板字面量类型支持字符串类型操作
- 类型守卫确保运行时类型安全
- 工具类型简化常见类型操作

掌握这些高级类型技巧能够构建更加类型安全和表达力强的代码。

---

**下一章：[项目实战：待办事项应用](../13-todo-project/README.md)**
