# 第13章：项目实战 - 待办事项应用

## 🎯 项目目标
- 综合运用前面学到的TypeScript知识
- 构建一个完整的待办事项管理应用
- 实践面向对象编程和模块化开发
- 学习项目结构组织和最佳实践

## 📋 项目需求

### 功能需求
- ✅ 添加待办事项
- ✅ 标记完成/未完成
- ✅ 编辑待办事项
- ✅ 删除待办事项
- ✅ 按状态筛选（全部/进行中/已完成）
- ✅ 数据持久化（localStorage）
- ✅ 统计信息显示

### 技术要求
- 使用TypeScript开发
- 面向对象设计
- 模块化架构
- 类型安全
- 错误处理

## 🏗️ 项目结构

```
13-todo-project/
├── src/
│   ├── models/
│   │   ├── Todo.ts
│   │   └── index.ts
│   ├── services/
│   │   ├── TodoService.ts
│   │   ├── StorageService.ts
│   │   └── index.ts
│   ├── utils/
│   │   ├── validators.ts
│   │   ├── helpers.ts
│   │   └── index.ts
│   ├── types/
│   │   ├── common.ts
│   │   └── index.ts
│   ├── app.ts
│   └── main.ts
├── public/
│   ├── index.html
│   └── style.css
├── package.json
└── tsconfig.json
```

## 📝 实现步骤

### 第一步：定义类型和模型

```typescript
// src/types/common.ts
export type TodoStatus = "pending" | "completed";

export type FilterType = "all" | "pending" | "completed";

export interface TodoData {
    id: string;
    title: string;
    description?: string;
    status: TodoStatus;
    createdAt: Date;
    updatedAt: Date;
    dueDate?: Date;
    priority: "low" | "medium" | "high";
}

export interface TodoCreateInput {
    title: string;
    description?: string;
    dueDate?: Date;
    priority?: "low" | "medium" | "high";
}

export interface TodoUpdateInput {
    title?: string;
    description?: string;
    status?: TodoStatus;
    dueDate?: Date;
    priority?: "low" | "medium" | "high";
}

export interface TodoStats {
    total: number;
    pending: number;
    completed: number;
    overdue: number;
}
```

```typescript
// src/models/Todo.ts
import { TodoData, TodoStatus, TodoCreateInput, TodoUpdateInput } from '../types';

export class Todo implements TodoData {
    public readonly id: string;
    public title: string;
    public description?: string;
    public status: TodoStatus;
    public readonly createdAt: Date;
    public updatedAt: Date;
    public dueDate?: Date;
    public priority: "low" | "medium" | "high";

    constructor(data: TodoCreateInput) {
        this.id = this.generateId();
        this.title = data.title;
        this.description = data.description;
        this.status = "pending";
        this.createdAt = new Date();
        this.updatedAt = new Date();
        this.dueDate = data.dueDate;
        this.priority = data.priority || "medium";
    }

    private generateId(): string {
        return `todo_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }

    public update(data: TodoUpdateInput): void {
        if (data.title !== undefined) this.title = data.title;
        if (data.description !== undefined) this.description = data.description;
        if (data.status !== undefined) this.status = data.status;
        if (data.dueDate !== undefined) this.dueDate = data.dueDate;
        if (data.priority !== undefined) this.priority = data.priority;
        
        this.updatedAt = new Date();
    }

    public toggleStatus(): void {
        this.status = this.status === "pending" ? "completed" : "pending";
        this.updatedAt = new Date();
    }

    public isOverdue(): boolean {
        if (!this.dueDate || this.status === "completed") {
            return false;
        }
        return new Date() > this.dueDate;
    }

    public getDaysUntilDue(): number | null {
        if (!this.dueDate) return null;
        
        const now = new Date();
        const due = new Date(this.dueDate);
        const diffTime = due.getTime() - now.getTime();
        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
        
        return diffDays;
    }

    public toJSON(): TodoData {
        return {
            id: this.id,
            title: this.title,
            description: this.description,
            status: this.status,
            createdAt: this.createdAt,
            updatedAt: this.updatedAt,
            dueDate: this.dueDate,
            priority: this.priority
        };
    }

    public static fromJSON(data: TodoData): Todo {
        const todo = Object.create(Todo.prototype);
        Object.assign(todo, {
            ...data,
            createdAt: new Date(data.createdAt),
            updatedAt: new Date(data.updatedAt),
            dueDate: data.dueDate ? new Date(data.dueDate) : undefined
        });
        return todo;
    }
}
```

### 第二步：实现服务层

```typescript
// src/services/StorageService.ts
export class StorageService {
    private readonly storageKey: string;

    constructor(key: string = "todos") {
        this.storageKey = key;
    }

    public save<T>(data: T): void {
        try {
            const jsonData = JSON.stringify(data);
            localStorage.setItem(this.storageKey, jsonData);
        } catch (error) {
            console.error("保存数据失败:", error);
            throw new Error("无法保存数据到本地存储");
        }
    }

    public load<T>(): T | null {
        try {
            const jsonData = localStorage.getItem(this.storageKey);
            if (!jsonData) return null;
            
            return JSON.parse(jsonData) as T;
        } catch (error) {
            console.error("加载数据失败:", error);
            return null;
        }
    }

    public clear(): void {
        localStorage.removeItem(this.storageKey);
    }

    public exists(): boolean {
        return localStorage.getItem(this.storageKey) !== null;
    }
}
```

```typescript
// src/services/TodoService.ts
import { Todo } from '../models/Todo';
import { StorageService } from './StorageService';
import { TodoCreateInput, TodoUpdateInput, FilterType, TodoStats, TodoData } from '../types';

export class TodoService {
    private todos: Todo[] = [];
    private storageService: StorageService;

    constructor() {
        this.storageService = new StorageService("todos");
        this.loadTodos();
    }

    public createTodo(input: TodoCreateInput): Todo {
        if (!input.title.trim()) {
            throw new Error("待办事项标题不能为空");
        }

        const todo = new Todo(input);
        this.todos.push(todo);
        this.saveTodos();
        
        return todo;
    }

    public getTodos(filter: FilterType = "all"): Todo[] {
        switch (filter) {
            case "pending":
                return this.todos.filter(todo => todo.status === "pending");
            case "completed":
                return this.todos.filter(todo => todo.status === "completed");
            default:
                return [...this.todos];
        }
    }

    public getTodoById(id: string): Todo | null {
        return this.todos.find(todo => todo.id === id) || null;
    }

    public updateTodo(id: string, input: TodoUpdateInput): Todo {
        const todo = this.getTodoById(id);
        if (!todo) {
            throw new Error(`待办事项 ${id} 不存在`);
        }

        if (input.title !== undefined && !input.title.trim()) {
            throw new Error("待办事项标题不能为空");
        }

        todo.update(input);
        this.saveTodos();
        
        return todo;
    }

    public deleteTodo(id: string): boolean {
        const index = this.todos.findIndex(todo => todo.id === id);
        if (index === -1) {
            return false;
        }

        this.todos.splice(index, 1);
        this.saveTodos();
        
        return true;
    }

    public toggleTodoStatus(id: string): Todo {
        const todo = this.getTodoById(id);
        if (!todo) {
            throw new Error(`待办事项 ${id} 不存在`);
        }

        todo.toggleStatus();
        this.saveTodos();
        
        return todo;
    }

    public getStats(): TodoStats {
        const total = this.todos.length;
        const pending = this.todos.filter(todo => todo.status === "pending").length;
        const completed = this.todos.filter(todo => todo.status === "completed").length;
        const overdue = this.todos.filter(todo => todo.isOverdue()).length;

        return { total, pending, completed, overdue };
    }

    public clearCompleted(): number {
        const completedCount = this.todos.filter(todo => todo.status === "completed").length;
        this.todos = this.todos.filter(todo => todo.status !== "completed");
        this.saveTodos();
        
        return completedCount;
    }

    private saveTodos(): void {
        const todoData = this.todos.map(todo => todo.toJSON());
        this.storageService.save(todoData);
    }

    private loadTodos(): void {
        const todoData = this.storageService.load<TodoData[]>();
        if (todoData) {
            this.todos = todoData.map(data => Todo.fromJSON(data));
        }
    }
}
```

### 第三步：实现应用主类

```typescript
// src/app.ts
import { TodoService } from './services/TodoService';
import { Todo } from './models/Todo';
import { FilterType, TodoCreateInput } from './types';

export class TodoApp {
    private todoService: TodoService;
    private currentFilter: FilterType = "all";

    constructor() {
        this.todoService = new TodoService();
        this.init();
    }

    private init(): void {
        this.setupEventListeners();
        this.render();
    }

    private setupEventListeners(): void {
        // 添加待办事项表单
        const addForm = document.getElementById('add-todo-form') as HTMLFormElement;
        addForm?.addEventListener('submit', this.handleAddTodo.bind(this));

        // 筛选按钮
        const filterButtons = document.querySelectorAll('.filter-btn');
        filterButtons.forEach(btn => {
            btn.addEventListener('click', this.handleFilterChange.bind(this));
        });

        // 清除已完成按钮
        const clearBtn = document.getElementById('clear-completed');
        clearBtn?.addEventListener('click', this.handleClearCompleted.bind(this));
    }

    private handleAddTodo(event: Event): void {
        event.preventDefault();

        const form = event.target as HTMLFormElement;
        const formData = new FormData(form);

        const input: TodoCreateInput = {
            title: formData.get('title') as string,
            description: formData.get('description') as string || undefined,
            priority: formData.get('priority') as "low" | "medium" | "high" || "medium",
            dueDate: formData.get('dueDate') ? new Date(formData.get('dueDate') as string) : undefined
        };

        try {
            this.todoService.createTodo(input);
            form.reset();
            this.render();
        } catch (error) {
            this.showError(error instanceof Error ? error.message : "添加失败");
        }
    }

    private handleFilterChange(event: Event): void {
        const button = event.target as HTMLButtonElement;
        const filter = button.dataset.filter as FilterType;

        if (filter) {
            this.currentFilter = filter;
            this.updateFilterButtons();
            this.renderTodoList();
        }
    }

    private handleClearCompleted(): void {
        const count = this.todoService.clearCompleted();
        if (count > 0) {
            this.showSuccess(`已清除 ${count} 个已完成的待办事项`);
            this.render();
        }
    }

    private handleToggleTodo(id: string): void {
        try {
            this.todoService.toggleTodoStatus(id);
            this.render();
        } catch (error) {
            this.showError(error instanceof Error ? error.message : "操作失败");
        }
    }

    private handleDeleteTodo(id: string): void {
        if (confirm("确定要删除这个待办事项吗？")) {
            const success = this.todoService.deleteTodo(id);
            if (success) {
                this.showSuccess("待办事项已删除");
                this.render();
            } else {
                this.showError("删除失败");
            }
        }
    }

    private render(): void {
        this.renderStats();
        this.renderTodoList();
        this.updateFilterButtons();
    }

    private renderStats(): void {
        const stats = this.todoService.getStats();
        const statsElement = document.getElementById('stats');

        if (statsElement) {
            statsElement.innerHTML = `
                <div class="stat-item">
                    <span class="stat-label">总计:</span>
                    <span class="stat-value">${stats.total}</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">进行中:</span>
                    <span class="stat-value">${stats.pending}</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">已完成:</span>
                    <span class="stat-value">${stats.completed}</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">逾期:</span>
                    <span class="stat-value ${stats.overdue > 0 ? 'overdue' : ''}">${stats.overdue}</span>
                </div>
            `;
        }
    }

    private renderTodoList(): void {
        const todos = this.todoService.getTodos(this.currentFilter);
        const listElement = document.getElementById('todo-list');

        if (!listElement) return;

        if (todos.length === 0) {
            listElement.innerHTML = '<div class="empty-state">暂无待办事项</div>';
            return;
        }

        listElement.innerHTML = todos.map(todo => this.renderTodoItem(todo)).join('');

        // 绑定事件
        this.bindTodoEvents();
    }

    private renderTodoItem(todo: Todo): string {
        const daysUntilDue = todo.getDaysUntilDue();
        const isOverdue = todo.isOverdue();

        return `
            <div class="todo-item ${todo.status} ${isOverdue ? 'overdue' : ''}" data-id="${todo.id}">
                <div class="todo-content">
                    <input type="checkbox" class="todo-checkbox" ${todo.status === 'completed' ? 'checked' : ''}>
                    <div class="todo-details">
                        <h3 class="todo-title">${this.escapeHtml(todo.title)}</h3>
                        ${todo.description ? `<p class="todo-description">${this.escapeHtml(todo.description)}</p>` : ''}
                        <div class="todo-meta">
                            <span class="priority priority-${todo.priority}">${this.getPriorityText(todo.priority)}</span>
                            ${todo.dueDate ? `<span class="due-date">${this.formatDate(todo.dueDate)}</span>` : ''}
                            ${daysUntilDue !== null ? `<span class="days-until-due">${this.formatDaysUntilDue(daysUntilDue)}</span>` : ''}
                        </div>
                    </div>
                </div>
                <div class="todo-actions">
                    <button class="btn btn-edit" data-action="edit">编辑</button>
                    <button class="btn btn-delete" data-action="delete">删除</button>
                </div>
            </div>
        `;
    }

    private bindTodoEvents(): void {
        const todoItems = document.querySelectorAll('.todo-item');

        todoItems.forEach(item => {
            const id = item.getAttribute('data-id');
            if (!id) return;

            // 复选框事件
            const checkbox = item.querySelector('.todo-checkbox') as HTMLInputElement;
            checkbox?.addEventListener('change', () => this.handleToggleTodo(id));

            // 按钮事件
            const buttons = item.querySelectorAll('[data-action]');
            buttons.forEach(btn => {
                btn.addEventListener('click', (e) => {
                    const action = (e.target as HTMLElement).getAttribute('data-action');
                    if (action === 'delete') {
                        this.handleDeleteTodo(id);
                    } else if (action === 'edit') {
                        this.handleEditTodo(id);
                    }
                });
            });
        });
    }

    private handleEditTodo(id: string): void {
        // 简单的编辑实现 - 在实际项目中可以使用模态框
        const todo = this.todoService.getTodoById(id);
        if (!todo) return;

        const newTitle = prompt("编辑标题:", todo.title);
        if (newTitle && newTitle.trim()) {
            try {
                this.todoService.updateTodo(id, { title: newTitle.trim() });
                this.render();
            } catch (error) {
                this.showError(error instanceof Error ? error.message : "编辑失败");
            }
        }
    }

    private updateFilterButtons(): void {
        const buttons = document.querySelectorAll('.filter-btn');
        buttons.forEach(btn => {
            const filter = btn.getAttribute('data-filter');
            btn.classList.toggle('active', filter === this.currentFilter);
        });
    }

    private escapeHtml(text: string): string {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    private getPriorityText(priority: string): string {
        const priorityMap = {
            low: "低",
            medium: "中",
            high: "高"
        };
        return priorityMap[priority as keyof typeof priorityMap] || priority;
    }

    private formatDate(date: Date): string {
        return date.toLocaleDateString('zh-CN');
    }

    private formatDaysUntilDue(days: number): string {
        if (days < 0) return `逾期 ${Math.abs(days)} 天`;
        if (days === 0) return "今天到期";
        if (days === 1) return "明天到期";
        return `${days} 天后到期`;
    }

    private showError(message: string): void {
        this.showMessage(message, 'error');
    }

    private showSuccess(message: string): void {
        this.showMessage(message, 'success');
    }

    private showMessage(message: string, type: 'error' | 'success'): void {
        const messageElement = document.createElement('div');
        messageElement.className = `message ${type}`;
        messageElement.textContent = message;

        document.body.appendChild(messageElement);

        setTimeout(() => {
            messageElement.remove();
        }, 3000);
    }
}
```

### 第四步：创建入口文件和配置

```typescript
// src/main.ts
import { TodoApp } from './app';

// 等待DOM加载完成
document.addEventListener('DOMContentLoaded', () => {
    new TodoApp();
});

// 导出类型供其他模块使用
export * from './types';
export * from './models';
export * from './services';
```

```typescript
// src/utils/validators.ts
export class Validators {
    static isValidEmail(email: string): boolean {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }

    static isValidDate(date: string): boolean {
        const parsedDate = new Date(date);
        return !isNaN(parsedDate.getTime()) && parsedDate > new Date();
    }

    static isNonEmptyString(value: string): boolean {
        return typeof value === 'string' && value.trim().length > 0;
    }

    static isValidPriority(priority: string): priority is "low" | "medium" | "high" {
        return ["low", "medium", "high"].includes(priority);
    }
}
```

```json
// package.json
{
  "name": "typescript-todo-app",
  "version": "1.0.0",
  "description": "TypeScript待办事项应用",
  "main": "dist/main.js",
  "scripts": {
    "build": "tsc",
    "dev": "tsc --watch",
    "start": "npm run build && node dist/main.js",
    "clean": "rm -rf dist"
  },
  "devDependencies": {
    "typescript": "^5.0.0",
    "@types/node": "^18.0.0"
  },
  "keywords": ["typescript", "todo", "app"],
  "author": "Your Name",
  "license": "MIT"
}
```

```json
// tsconfig.json
{
  "compilerOptions": {
    "target": "ES2020",
    "module": "ES2020",
    "moduleResolution": "node",
    "outDir": "./dist",
    "rootDir": "./src",
    "strict": true,
    "esModuleInterop": true,
    "skipLibCheck": true,
    "forceConsistentCasingInFileNames": true,
    "declaration": true,
    "declarationMap": true,
    "sourceMap": true,
    "removeComments": false,
    "noImplicitAny": true,
    "noImplicitReturns": true,
    "noImplicitThis": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "exactOptionalPropertyTypes": true,
    "lib": ["ES2020", "DOM"]
  },
  "include": [
    "src/**/*"
  ],
  "exclude": [
    "node_modules",
    "dist"
  ]
}
```

## 🎨 HTML和CSS文件

```html
<!-- public/index.html -->
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TypeScript 待办事项</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <div class="container">
        <header>
            <h1>📝 待办事项管理</h1>
            <div id="stats" class="stats"></div>
        </header>

        <main>
            <section class="add-todo-section">
                <form id="add-todo-form" class="add-todo-form">
                    <div class="form-group">
                        <input type="text" name="title" placeholder="输入待办事项..." required>
                    </div>
                    <div class="form-group">
                        <textarea name="description" placeholder="描述（可选）"></textarea>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <select name="priority">
                                <option value="low">低优先级</option>
                                <option value="medium" selected>中优先级</option>
                                <option value="high">高优先级</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <input type="date" name="dueDate">
                        </div>
                        <button type="submit" class="btn btn-primary">添加</button>
                    </div>
                </form>
            </section>

            <section class="filter-section">
                <div class="filter-buttons">
                    <button class="filter-btn active" data-filter="all">全部</button>
                    <button class="filter-btn" data-filter="pending">进行中</button>
                    <button class="filter-btn" data-filter="completed">已完成</button>
                </div>
                <button id="clear-completed" class="btn btn-secondary">清除已完成</button>
            </section>

            <section class="todo-section">
                <div id="todo-list" class="todo-list"></div>
            </section>
        </main>
    </div>

    <script type="module" src="../dist/main.js"></script>
</body>
</html>
```

## 🚀 运行项目

### 1. 安装依赖
```bash
npm install
```

### 2. 编译TypeScript
```bash
npm run build
```

### 3. 开发模式（监听文件变化）
```bash
npm run dev
```

### 4. 在浏览器中打开
打开 `public/index.html` 文件

## 📚 项目总结

### 学到的知识点
1. **类型定义**: 使用接口和类型别名定义数据结构
2. **面向对象**: 使用类封装业务逻辑
3. **模块化**: 合理组织代码结构
4. **错误处理**: 使用try-catch和类型守卫
5. **数据持久化**: localStorage的使用
6. **事件处理**: DOM事件的类型安全处理

### 最佳实践
- ✅ 严格的类型检查
- ✅ 单一职责原则
- ✅ 依赖注入模式
- ✅ 错误边界处理
- ✅ 代码复用和模块化
- ✅ 用户体验优化

### 可扩展功能
- 🔄 添加拖拽排序
- 🏷️ 标签分类系统
- 🔍 搜索功能
- 📊 数据统计图表
- 🌙 主题切换
- 📱 响应式设计
- 🔄 数据同步（后端API）

这个项目展示了如何使用TypeScript构建一个完整的前端应用，涵盖了类型安全、面向对象编程、模块化架构等核心概念。

## 🎯 恭喜完成学习！

通过这13个章节的学习，您已经掌握了TypeScript的核心知识和实践技能。继续练习和探索更多高级特性，成为TypeScript专家！

---

**🎉 TypeScript学习教程完成！**
```
