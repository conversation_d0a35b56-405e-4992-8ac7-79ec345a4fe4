// 通用类型定义

export type TodoStatus = "pending" | "completed";

export type FilterType = "all" | "pending" | "completed";

export interface TodoData {
    id: string;
    title: string;
    description?: string;
    status: TodoStatus;
    createdAt: Date;
    updatedAt: Date;
    dueDate?: Date;
    priority: "low" | "medium" | "high";
}

export interface TodoCreateInput {
    title: string;
    description?: string;
    dueDate?: Date;
    priority?: "low" | "medium" | "high";
}

export interface TodoUpdateInput {
    title?: string;
    description?: string;
    status?: TodoStatus;
    dueDate?: Date;
    priority?: "low" | "medium" | "high";
}

export interface TodoStats {
    total: number;
    pending: number;
    completed: number;
    overdue: number;
}
