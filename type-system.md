# TypeScript 类型系统图解

## 1. 类型层次结构与关系

### 1.1 基础类型层次

下图展示了 TypeScript 中的类型层次结构：

```mermaid
graph TD
    A[any] --> B[Object]
    A --> C[Number]
    A --> D[String]
    A --> E[Boolean]
    A --> F[Array]
    A --> G[Function]
    A --> H[Void]
    A --> I[Null]
    A --> J[Undefined]
    A --> K[Never]
    
    B --> L[Interface]
    B --> M[Class]
    F --> N[TypedArray]
    L --> O[Custom Interface]
    M --> P[Custom Class]
```

### 代码示例：

```typescript
// 基础类型示例
let anyValue: any = "可以是任何类型";
let numberValue: number = 42;
let stringValue: string = "Hello";
let booleanValue: boolean = true;

// 对象类型示例
interface Person {
    name: string;
    age: number;
}

class Employee implements Person {
    constructor(public name: string, public age: number) {}
}

// 数组类型示例
let numbers: number[] = [1, 2, 3];
let strings: Array<string> = ["a", "b", "c"];

// 函数类型示例
type MathOperation = (a: number, b: number) => number;
```

### 1.2 类型关系图

```mermaid
graph TB
    subgraph "顶层类型"
    any --> unknown
    end
    
    subgraph "基础类型"
    unknown --> number
    unknown --> string
    unknown --> boolean
    unknown --> bigint
    unknown --> symbol
    end
    
    subgraph "对象类型"
    unknown --> object
    object --> array[Array]
    object --> function[Function]
    object --> class[Class]
    object --> interface[Interface]
    end
    
    subgraph "特殊类型"
    unknown --> null
    unknown --> undefined
    unknown --> void
    unknown --> never
    end
```

## 类型转换流程

```mermaid
flowchart LR
    A[源类型] --> B{类型兼容性检查}
    B -->|兼容| C[直接赋值]
    B -->|不兼容| D{是否可以类型断言}
    D -->|可以| E[使用类型断言]
    D -->|不可以| F[类型错误]
```

### 2.1 类型转换示例

```typescript
// 1. 直接赋值（类型兼容）
let num: number = 42;
let float: number = num;  // OK

// 2. 类型断言
let someValue: unknown = "这是一个字符串";
let strLength: number = (someValue as string).length;

// 3. 类型保护
function processValue(val: string | number) {
    if (typeof val === "string") {
        // 在这个块中，val 的类型被收窄为 string
        console.log(val.toUpperCase());
    } else {
        // 在这个块中，val 的类型被收窄为 number
        console.log(val.toFixed(2));
    }
}
```

### 2.2 转换规则说明

- 向上转换（Upcasting）：总是安全的
- 向下转换（Downcasting）：需要类型断言
- 类型保护：使用 instanceof、typeof、in 等操作符

## 3. 泛型工作原理

```mermaid
sequenceDiagram
    participant C as 调用者
    participant F as 泛型函数
    participant T as 类型参数
    
    C->>F: 调用函数并指定类型
    F->>T: 保存类型信息
    F->>F: 使用类型检查
    F->>C: 返回类型安全的结果
```

### 3.1 泛型示例

```typescript
// 泛型函数
function identity<T>(arg: T): T {
    return arg;
}

// 泛型接口
interface Container<T> {
    value: T;
    getValue(): T;
}

// 泛型类
class Box<T> {
    private content: T;
    
    constructor(value: T) {
        this.content = value;
    }
    
    getValue(): T {
        return this.content;
    }
}

// 泛型约束
interface Lengthwise {
    length: number;
}

function logLength<T extends Lengthwise>(arg: T): number {
    return arg.length;
}
```

### 3.2 泛型工作流程图

```mermaid
stateDiagram-v2
    [*] --> 定义泛型
    定义泛型 --> 使用泛型: 指定具体类型
    使用泛型 --> 类型检查: 编译时检查
    类型检查 --> 类型推断: 可选
    类型检查 --> 代码生成: 类型擦除
    代码生成 --> [*]
```

## 4. 类型推断机制

```mermaid
flowchart TB
    A[变量声明] --> B{有显式类型注解?}
    B -->|是| C[使用注解类型]
    B -->|否| D{有初始值?}
    D -->|是| E[从值推断类型]
    D -->|否| F[推断为 any]
    E --> G[最佳通用类型算法]
    G --> H[上下文类型检查]
```

### 4.1 类型推断示例

```typescript
// 基础类型推断
let x = 3;  // 推断为 number
let y = "hello";  // 推断为 string
let z = true;  // 推断为 boolean

// 数组类型推断
let arr = [1, 2, 3];  // 推断为 number[]
let mixed = [1, "hello", true];  // 推断为 (number | string | boolean)[]

// 对象类型推断
let person = {
    name: "张三",
    age: 25,
    getAge() { return this.age; }
};  // 推断出完整的对象类型

// 上下文类型推断
window.onmousedown = function(mouseEvent) {
    console.log(mouseEvent.button);  // mouseEvent 被推断为 MouseEvent
};
```

### 4.2 推断规则说明

1. 声明时推断
   - 变量声明
   - 函数返回值
   - 成员类型

2. 赋值时推断
   - 最佳通用类型算法
   - 上下文类型推断

3. 结构化类型推断
   - 对象字面量
   - 数组字面量
   - 函数表达式

## 5. 高级类型关系图

```mermaid
graph LR
    subgraph "联合与交叉类型"
    U[联合类型 A | B]
    I[交叉类型 A & B]
    end
    
    subgraph "条件类型"
    C[T extends U ? X : Y]
    M[映射类型]
    end
    
    subgraph "实用类型"
    P[Partial<T>]
    R[Required<T>]
    O[Omit<T, K>]
    Pi[Pick<T, K>]
    end
    
    U --> C
    I --> M
    C --> P
    M --> R
    P --> O
    R --> Pi
```

### 代码示例：高级类型

```typescript
// 联合类型
type StringOrNumber = string | number;

// 交叉类型
type PersonWithAge = Person & { age: number };

// 条件类型
type TypeName<T> = T extends string ? "string" :
                   T extends number ? "number" :
                   T extends boolean ? "boolean" :
                   "object";

// 映射类型
type Optional<T> = {
    [P in keyof T]?: T[P];
};

// 实用类型示例
interface Todo {
    title: string;
    description: string;
    completed: boolean;
}

type PartialTodo = Partial<Todo>;  // 所有属性变为可选
type RequiredTodo = Required<Todo>;  // 所有属性变为必需
```

## 6. 最佳实践建议

### 6.1 类型使用决策流程

```mermaid
flowchart TD
    A[开始] --> B{是否需要类型安全?}
    B -->|是| C{是否为已知类型?}
    B -->|否| D[使用 any]
    C -->|是| E[使用具体类型]
    C -->|否| F{是否需要类型转换?}
    F -->|是| G[使用类型断言]
    F -->|否| H{是否可重用?}
    H -->|是| I[使用泛型]
    H -->|否| J[使用联合类型]
```

### 6.2 实践要点

1. **类型优先级**
   - 优先使用具体类型
   - 其次考虑泛型
   - 最后才使用 any

2. **类型安全性**
   - 避免过度使用类型断言
   - 合理使用类型保护
   - 利用 TypeScript 的类型推断

3. **代码可维护性**
   - 为复杂类型创建类型别名
   - 使用接口代替类型别名（当可能时）
   - 保持类型定义的一致性

## 7. 练习与实践

### 7.1 推荐练习

1. 类型层次结构练习
```typescript
// 练习：实现一个类型层次结构
interface Animal {
    name: string;
}

interface Pet extends Animal {
    owner: string;
}

interface Dog extends Pet {
    breed: string;
    bark(): void;
}
```

2. 类型转换练习
```typescript
// 练习：安全的类型转换
function processUserInput(input: unknown) {
    if (typeof input === "string") {
        return input.toUpperCase();
    } else if (typeof input === "number") {
        return input.toFixed(2);
    } else {
        throw new Error("不支持的输入类型");
    }
}
```

3. 泛型练习
```typescript
// 练习：实现通用的状态容器
class StateContainer<T> {
    private state: T;

    constructor(initialState: T) {
        this.state = initialState;
    }

    getState(): T {
        return this.state;
    }

    setState(newState: T): void {
        this.state = newState;
    }
}
```

### 7.2 进阶练习建议

1. 实现一个类型安全的事件发布订阅系统
2. 创建一个通用的数据验证器
3. 构建类型安全的 API 客户端

记住：类型系统是为了帮助我们写出更好的代码，而不是阻碍开发效率。在实践中要平衡类型安全和开发效率。
